"""
Characters

Characters are (by default) Objects setup to be puppeted by Accounts.
They are what you "see" in game. The Character class in this module
is setup to be the "default" character type created by the default
creation commands.

"""
from evennia.objects.objects import DefaultCharacter # type: ignore
from evennia.contrib.base_systems.components import ComponentHolderMixin  # type: ignore
from evennia.utils.utils import lazy_property # type: ignore
from evennia.contrib.rpg.traits import TraitHandler  # type: ignore

from .objects import ObjectParent
from components.cultivation_component import CultivationComponent


class Character(ObjectParent, DefaultCharacter, ComponentHolderMixin):
    """
    Modernized MUD Character Class

    This class represents the player character, built upon Evennia's
    recommended best practices using Components and Traits.

    - `ComponentHolderMixin`: Allows attaching modular `Components`.
    - `TraitHandler`: Manages all numerical stats and attributes.
    """

    def at_object_creation(self):
        """Called only when the object is first created."""
        super().at_object_creation()

        # Initialize all numerical traits
        self.initialize_traits()

        # Add components to the character
        self.components.add(CultivationComponent)

        # Set initial db flags
        self.db.is_cultivating = False
        self.db.cultivation_technique = None
        self.db.cultivation_start_time = 0

    @lazy_property
    def traits(self) -> TraitHandler:
        """Adds the TraitHandler to the character."""
        return TraitHandler(self)

    def initialize_traits(self):
        """
        Initializes all the numerical traits for the character.
        This is called only once, during character creation.
        """
        # Core Cultivation Traits
        self.traits.add("realm", "境界", "static", "练气期") # type: ignore
        self.traits.add("realm_level", "境界等级", "static", 1) # type: ignore
        self.traits.add("realm_tier", "境界阶位", "static", 1) # type: ignore
        self.traits.add("cultivation", "修为", "gauge", 0) # type: ignore

        # Core Combat/Vital Traits
        self.traits.add("hp", "生命值", "gauge", 100) # type: ignore
        self.traits.add("sp", "灵力", "gauge", 100) # type: ignore

        # Elemental Affinities (as static traits)
        self.traits.add("fire_affinity", "火系亲和", "static", 0.1) # type: ignore
        self.traits.add("water_affinity", "水系亲和", "static", 0.1) # type: ignore
        self.traits.add("earth_affinity", "土系亲和", "static", 0.1) # type: ignore
        self.traits.add("wood_affinity", "木系亲和", "static", 0.1) # type: ignore
        self.traits.add("metal_affinity", "金系亲和", "static", 0.1) # type: ignore
        
    def get_cultivation_info(self):
        """Convenience method to get cultivation info from the component."""
        if self.components.has("cultivation"):
            return self.components.cultivation.get_info()
        return {"Error": "Cultivation component not found."}

    def get_full_status(self):
        """
        Gathers and returns a complete status of the character.
        """
        return {
            "basic_info": {
                "name": self.name,
                "location": str(self.location) if self.location else "未知"
            },
            "cultivation": self.get_cultivation_info(),
            "vitals": {
                "HP": self.traits.hp.current, # type: ignore
                "SP": self.traits.sp.current # type: ignore
            }
        }

#!/bin/bash

echo "=== 全面测试AI导演命令 ==="

# 测试命令列表
commands=(
    "ai帮助"
    "ai状态"
    "ai性能"
    "测试ai"
    "ai决策 玩家遇到了神秘的修仙洞府"
    "解析故事 《逆天改命》主题：凡人修仙，境界提升，最终成为仙帝"
    "ai重载"
)

success_count=0
total_count=${#commands[@]}

for i in "${!commands[@]}"; do
    cmd="${commands[$i]}"
    cmd_num=$((i + 1))
    
    echo ""
    echo "[$cmd_num/$total_count] 测试命令: $cmd"
    echo "执行中..."
    
    # 使用expect脚本来自动化telnet会话
    result=$(timeout 10s bash -c "
        {
            echo 'connect admin 123'
            sleep 2
            echo '$cmd'
            sleep 3
            echo 'quit'
        } | nc localhost 4000 2>/dev/null
    ")
    
    # 检查是否有错误
    if echo \"$result\" | grep -q \"Traceback\\|Error\\|Failed\"; then
        echo \"✗ 命令执行失败\"
        echo \"错误输出:\"
        echo \"$result\" | grep -A 5 -B 5 \"Traceback\\|Error\\|Failed\"
    else
        echo \"✓ 命令执行成功\"
        ((success_count++))
    fi
    
    # 短暂暂停避免连接冲突
    sleep 1
done

echo ""
echo "=================================================="
echo "测试总结:"
echo "总命令数: $total_count"
echo "成功执行: $success_count"
echo "失败执行: $((total_count - success_count))"
echo "成功率: $(( success_count * 100 / total_count ))%"

if [ $success_count -eq $total_count ]; then
    echo "🎉 所有AI导演命令测试通过！"
    exit 0
else
    echo "❌ 部分命令测试失败"
    exit 1
fi
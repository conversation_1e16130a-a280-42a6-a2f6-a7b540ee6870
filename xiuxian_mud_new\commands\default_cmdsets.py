"""
Command sets

All commands in the game must be grouped in a cmdset.  A given command
can be part of any number of cmdsets and cmdsets can be added/removed
and merged onto entities at runtime.

To create new commands to populate the cmdset, see
`commands/command.py`.

This module wraps the default command sets of Evennia; overloads them
to add/remove commands from the default lineup. You can create your
own cmdsets by inheriting from them or directly from `evennia.CmdSet`.

"""

from evennia.commands.default import (
    cmdset_character,
    cmdset_account,
    cmdset_session,
    cmdset_unloggedin,
)
# from . import utility  # 导入新的utility模块
from commands import ai_director_commands  # 导入AI导演命令模块
from evennia.commands.cmdset import CmdSet


class CharacterCmdSet(cmdset_character.CharacterCmdSet):
    """
    The `CharacterCmdSet` contains commands normal Characters can use.
    """

    key = "DefaultCharacter"

    def at_cmdset_creation(self):
        """
        Populates the cmdset
        """
        super().at_cmdset_creation()
        #
        # any commands you add here will be available to all characters.
        #
        
        # Add the refactored AI director commands
        self.add(ai_director_commands.CmdAIDirectorStatus())
        self.add(ai_director_commands.CmdAIDirectorAnalyze())
        self.add(ai_director_commands.CmdAIDirectorDecision())
        self.add(ai_director_commands.CmdAIDirectorPerformance())
        self.add(ai_director_commands.CmdAIDirectorReload())
        
        # Note: CmdAIDirectorPerformance and CmdAIDirectorReload exist but might be
        # better suited for an Admin-only cmdset. Adding them here for now
        # as they are protected by builder-level locks.
        # If you want to check them, you can uncomment the lines below.
        # from evennia.commands.default import general
        # self.add(general.CmdHelp())


class AccountCmdSet(cmdset_account.AccountCmdSet):
    """
    This is the cmdset available to the Account at all times. It is
    combined with the `CharacterCmdSet` when the Account puppets a
    Character. It should contain Account-general commands that are
    useful no matter which Character is currently being puppeted.
    """

    key = "DefaultAccount"

    def at_cmdset_creation(self):
        """
        Populates the cmdset
        """
        super().at_cmdset_creation()
        #
        # any commands you add here will be available to all accounts.
        #


class SessionCmdSet(cmdset_session.SessionCmdSet):
    """
    This cmdset is made available on Session objects. It is active before
    the Account has logged in.
    """

    key = "DefaultSession"

    def at_cmdset_creation(self):
        """
        Populates the cmdset
        """
        super().at_cmdset_creation()
        #
        # any commands you add here will be available to all sessions.
        #


class UnloggedinCmdSet(cmdset_unloggedin.UnloggedinCmdSet):
    """
    This is the cmdset available to the Session before being logged in.
    """

    key = "DefaultUnloggedin"

    def at_cmdset_creation(self):
        """
        Populates the cmdset
        """
        super().at_cmdset_creation()
        #
        # any commands you add here will be available to all unloggedin sessions.
        #

#!/usr/bin/env python3
"""
Simple startup script for Evennia server when evennia command is not available
"""
import os
import sys
import subprocess

# Add the parent venv to Python path
sys.path.insert(0, '/mnt/c/Users/<USER>/Documents/GitHub/NewEvennia/venv/Lib/site-packages')
os.environ['DJANGO_SETTINGS_MODULE'] = 'server.conf.settings'

def start_server():
    try:
        # Try to run via twisted/evennia directly
        from twisted.scripts.twistd import run
        from twisted.python import usage
        
        # Set up the server
        sys.argv = ['twistd', '--python=server/server.py', '--nodaemon']
        
        print("Starting Evennia server...")
        run()
        
    except Exception as e:
        print(f"Failed to start server: {e}")
        print("Trying alternative method...")
        
        # Alternative: try to run django development server
        try:
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
            from django.core.management import execute_from_command_line
            execute_from_command_line(['manage.py', 'runserver', '127.0.0.1:4001'])
        except Exception as e2:
            print(f"Alternative method also failed: {e2}")
            print("Please check if all dependencies are properly installed.")

if __name__ == '__main__':
    start_server()
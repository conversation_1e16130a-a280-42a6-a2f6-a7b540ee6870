"""
AI导演系统Web API
"""

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import json

try:
    from systems.ai_director import get_ai_director
    from systems.handlers.ai_director_handler import AIDirectorHandler
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False


@csrf_exempt
@require_http_methods(["POST"])
def parse_story_outline(request):
    """解析故事大纲API"""
    if not AI_AVAILABLE:
        return JsonResponse({
            'success': False,
            'error': 'AI导演系统未启用'
        })
    
    try:
        data = json.loads(request.body)
        outline_text = data.get('outline_text', '')
        
        if not outline_text:
            return JsonResponse({
                'success': False,
                'error': '请提供故事大纲文本'
            })
        
        # 获取AI导演
        director = get_ai_director()
        outline = director.analyze_story_outline(outline_text)
        
        return JsonResponse({
            'success': True,
            'outline_id': outline.outline_id,
            'title': outline.title,
            'theme': outline.theme,
            'main_conflict': outline.main_conflict,
            'key_characters': outline.key_characters,
            'plot_points': len(outline.major_plot_points),
            'phases': [phase.value for phase in outline.expected_phases]
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@csrf_exempt
@require_http_methods(["POST"])
def make_ai_decision(request):
    """生成AI决策API"""
    if not AI_AVAILABLE:
        return JsonResponse({
            'success': False,
            'error': 'AI导演系统未启用'
        })
    
    try:
        data = json.loads(request.body)
        
        # 获取AI导演
        director = get_ai_director()
        decision = director.make_decision(data)
        
        return JsonResponse({
            'success': True,
            'decision_id': decision.decision_id,
            'decision_type': decision.decision_type.value,
            'content': decision.content,
            'confidence': decision.confidence,
            'response_time': decision.response_time * 1000,  # 转换为毫秒
            'next_actions': decision.context.get('next_actions', [])
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@require_http_methods(["GET"])
def get_ai_stats(request):
    """获取AI性能统计API"""
    if not AI_AVAILABLE:
        return JsonResponse({
            'success': False,
            'error': 'AI导演系统未启用'
        })
    
    try:
        director = get_ai_director()
        stats = director.get_performance_stats()
        
        cache_total = stats['cache_hits'] + stats['cache_misses']
        cache_hit_rate = (stats['cache_hits'] / cache_total * 100) if cache_total > 0 else 0
        
        return JsonResponse({
            'success': True,
            'total_decisions': stats['total_decisions'],
            'avg_response_time': stats['average_response_time'] * 1000,
            'cache_hit_rate': cache_hit_rate,
            'cache_hits': stats['cache_hits'],
            'cache_misses': stats['cache_misses']
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@require_http_methods(["GET"])
def ai_director_status(request):
    """获取AI导演状态API"""
    if not AI_AVAILABLE:
        return JsonResponse({
            'success': False,
            'error': 'AI导演系统未启用'
        })
    
    try:
        from evennia import ObjectDB
        
        # 获取AI导演实例
        director = get_ai_director()
        
        # 获取性能统计
        stats = director.get_performance_stats()
        cache_total = stats.get('cache_hits', 0) + stats.get('cache_misses', 0)
        cache_hit_rate = (stats.get('cache_hits', 0) / cache_total * 100) if cache_total > 0 else 0
        
        # 获取在线玩家数量
        online_players = ObjectDB.objects.filter(
            db_typeclass_path__icontains='Character',
            db_sessions__isnull=False
        ).count()
        
        # 获取系统状态
        status_data = {
            'success': True,
            'status': 'active',
            'ai_available': True,
            'version': '1.0.0',
            'timestamp': director._last_activity if hasattr(director, '_last_activity') else None,
            'performance': {
                'total_decisions': stats.get('total_decisions', 0),
                'avg_response_time_ms': stats.get('average_response_time', 0) * 1000,
                'cache_hit_rate': cache_hit_rate,
                'cache_hits': stats.get('cache_hits', 0),
                'cache_misses': stats.get('cache_misses', 0)
            },
            'system': {
                'online_players': online_players,
                'total_objects': ObjectDB.objects.count(),
                'ai_director_active': True
            }
        }
        
        return JsonResponse(status_data)
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@require_http_methods(["GET"])
def ai_director_debug(request):
    """AI导演调试信息API"""
    if not AI_AVAILABLE:
        return JsonResponse({
            'success': False,
            'error': 'AI导演系统未启用'
        })
    
    try:
        from evennia import ObjectDB
        from django.conf import settings
        import sys
        import os
        
        director = get_ai_director()
        
        # 收集调试信息
        debug_info = {
            'success': True,
            'ai_director': {
                'instance_id': id(director),
                'class_name': director.__class__.__name__,
                'module_path': director.__class__.__module__,
                'methods': [method for method in dir(director) if not method.startswith('_')],
                'attributes': {
                    attr: str(getattr(director, attr))[:100] + '...' if len(str(getattr(director, attr))) > 100 else str(getattr(director, attr))
                    for attr in dir(director) 
                    if not attr.startswith('_') and not callable(getattr(director, attr))
                }
            },
            'handlers': {
                'ai_director_handlers': ObjectDB.objects.filter(
                    db_typeclass_path__icontains='AIDirectorHandler'
                ).count()
            },
            'system': {
                'python_version': sys.version,
                'evennia_version': getattr(settings, 'EVENNIA_VERSION', 'Unknown'),
                'debug_mode': settings.DEBUG,
                'database_engine': settings.DATABASES['default']['ENGINE'],
                'cache_backend': settings.CACHES['default']['BACKEND'],
                'working_directory': os.getcwd(),
                'ai_config_loaded': hasattr(settings, 'AI_CONFIG')
            },
            'runtime': {
                'total_objects': ObjectDB.objects.count(),
                'character_objects': ObjectDB.objects.filter(
                    db_typeclass_path__icontains='Character'
                ).count(),
                'script_objects': ObjectDB.objects.filter(
                    db_typeclass_path__icontains='Script'
                ).count()
            }
        }
        
        return JsonResponse(debug_info)
        
    except Exception as e:
        import traceback
        return JsonResponse({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        })
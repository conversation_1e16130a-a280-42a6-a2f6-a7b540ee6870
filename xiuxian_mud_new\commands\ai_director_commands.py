"""
AI导演相关命令
"""

from evennia.commands.command import Command # type: ignore
from evennia.commands.cmdset import CmdSet # type: ignore
from evennia.utils import logger, search, evtable # type: ignore

# This is a forward-declaration for the try-except block
get_ai_director = None

# We no longer import from the old director system.
# try:
#     from systems.ai_director import get_ai_director, reload_ai_director, AI_AVAILABLE
# except (ImportError, ModuleNotFoundError):
#     # This fallback is now handled by the global script's own checks.
#     AI_AVAILABLE = False


def get_ai_director_script():
    """Helper function to get the AI Director script."""
    scripts = search.search_script("ai_director_script")
    return scripts[0] if scripts else None


class CmdAIDirectorStatus(Command):
    """
    Check the status of the AI Director system.

    Usage:
      ai status

    Displays a table with the current operational status of the
    global AI Director script.
    """

    key = "ai status"
    locks = "cmd:perm(Builder)"
    help_category = "AI Director"

    def func(self):
        """Implement the command"""
        ai_script = get_ai_director_script()
        if not ai_script:
            self.caller.msg("AI Director script not found or not active.")
            return

        status = ai_script.get_status()
        
        table = evtable.EvTable(
            "|wAI Director Status|n",
            header=False,
            border="grid",
            table_width=78,
        )
        for key, value in status.items():
            table.add_row(f"|c{key.replace('_', ' ').title()}|n", str(value))
        
        self.caller.msg(str(table))


class CmdAIDirectorAnalyze(Command):
    """
    Analyze a story outline with the AI Director.

    Usage:
      ai analyze <story outline>

    This command is currently a placeholder to demonstrate the
    new architecture.
    """

    key = "ai analyze"
    locks = "cmd:perm(Builder)"
    help_category = "AI Director"

    def func(self):
        """Implement the command"""
        if not self.args:
            self.caller.msg("Usage: ai analyze <story outline>")
            return
        
        ai_script = get_ai_director_script()
        if not ai_script:
            self.caller.msg("AI Director script not found or not active.")
            return

        self.caller.msg("Sending story outline to AI Director for analysis...")
        
        # The script's `analyze_story_outline` method is not fully implemented yet.
        self.caller.msg("This command is pending full implementation with the new AI Script.")


class CmdAIDirectorDecision(Command):
    """
    Force the AI Director to make a narrative decision.

    Usage:
      ai decide

    Triggers the AI Director's main processing loop immediately,
    forcing it to analyze the world and make a decision.
    """

    key = "ai decide"
    locks = "cmd:perm(Builder)"
    help_category = "AI Director"

    def func(self):
        """Implement the command"""
        ai_script = get_ai_director_script()
        if not ai_script:
            self.caller.msg("AI Director script not found or not active.")
            return

        self.caller.msg("Forcing AI Director to make a narrative decision...")
        ai_script.force_run()
        self.caller.msg("AI Director cycle triggered.")


class CmdAIDirectorPerformance(Command):
    """
    Check the performance statistics of the AI Director.

    Usage:
      ai perf

    Displays performance data, if available on the script.
    """
    key = "ai perf"
    locks = "cmd:perm(Builder)"
    help_category = "AI Director"

    def func(self):
        """Implement the command"""
        ai_script = get_ai_director_script()
        if not ai_script:
            self.caller.msg("AI Director script not found or not active.")
            return

        if hasattr(ai_script, "get_performance_stats"):
            stats = ai_script.get_performance_stats()
            self.caller.msg(f"AI Director Performance: {stats}")
        else:
            self.caller.msg("Performance monitoring is not implemented on the current AI script.")


class CmdAIDirectorReload(Command):
    """
    Reloads the AI Director's configuration.

    Usage:
      ai reload

    Triggers a reload of the AI's configuration from source files.
    """
    key = "ai reload"
    locks = "cmd:perm(Builder)"
    help_category = "AI Director"

    def func(self):
        """Implement the command"""
        ai_script = get_ai_director_script()
        if not ai_script:
            self.caller.msg("AI Director script not found or not active.")
            return
        
        if hasattr(ai_script, "update_configuration"):
            # For now, we can just call update with its existing config
            # to simulate a reload. A more complex system would re-read from a file.
            ai_script.update_configuration(ai_script.db.config)
            self.caller.msg("AI Director configuration reloaded.")
        else:
            self.caller.msg("Configuration reload is not implemented on the current AI script.")


class AIDirectorCmdSet(CmdSet):
    """
    Holds all AI Director commands.
    """
    key = "AI Director CmdSet"
    
    def at_cmdset_creation(self):
        """Populates the cmdset."""
        self.add(CmdAIDirectorStatus())
        self.add(CmdAIDirectorAnalyze())
        self.add(CmdAIDirectorDecision())
        self.add(CmdAIDirectorPerformance())
        self.add(CmdAIDirectorReload())
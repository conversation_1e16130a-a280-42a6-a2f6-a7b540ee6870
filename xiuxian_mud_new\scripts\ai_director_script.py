"""
AI Director <PERSON> Script

This script acts as a central, persistent AI director for the game world.
It runs on a ticker, periodically observing the state of the world and
making narrative decisions based on its configuration and the actions of players.
"""

from evennia.scripts.scripts import DefaultScript  # type: ignore
from evennia.utils import logger  # type: ignore
from evennia.utils.search import search_object  # type: ignore
import json

try:
    from systems.ai_client import get_ai_client
    from ai_config import AIConfig
except (ImportError, ModuleNotFoundError):
    logger.log_err("AI Director Script could not import its dependencies.")
    get_ai_client = None
    AIConfig = None

class AIDirectorScript(DefaultScript):
    """
    The main script for the AI Director system.
    """

    def at_script_creation(self):
        """
        Called when the script is first created.
        """
        self.key = "ai_director_script"
        self.desc = "The main AI Director for the MUD."
        self.interval = 300  # seconds (5 minutes)
        self.persistent = True
        self.db.is_active = True
        self.db.world_story_outline = {}
        self.db.player_narratives = {}

        if AIConfig:
            self.db.config = AIConfig.get_story_config()
        else:
            self.db.config = {}

    def at_repeat(self):
        """
        This method is called every `self.interval` seconds.
        It's the main loop of the AI director.
        """
        if not self.db.is_active:
            return

        logger.log_info("AI Director: Beginning world state analysis...")

        world_data = self.collect_world_data()
        self.make_narrative_decision(world_data)

    def collect_world_data(self) -> dict:
        """
        Gathers relevant data from the game world.
        """
        players = search_object(key="*", typeclass="typeclasses.characters.Character")
        
        player_data = []
        for player in players:
            if player.has_account:
                player_info = {
                    "name": player.key,
                    "location": player.location.key if player.location else "None",
                }
                if player.components.has("cultivation"):
                    player_info.update(player.components.cultivation.get_info())
                player_data.append(player_info)

        return {
            "player_count": len(player_data),
            "players": player_data,
            "game_time": self.get_game_time(),
        }

    def make_narrative_decision(self, world_data: dict):
        """
        Uses the AI client to make a narrative decision.
        """
        if not get_ai_client:
            logger.log_warning("AI Director: AIClient (get_ai_client) not available, skipping narrative decision.")
            return

        logger.log_info(f"AI Director: Making narrative decision with data: {world_data}")
        
        client = get_ai_client()
        
        try:
            # The client's method is `ai_director_decision` and it takes the data dict directly
            decision_str = client.ai_director_decision(world_data)
            # The client returns a JSON string, so we need to parse it
            decision = json.loads(decision_str)
            self.execute_decision(decision)
        except Exception as e:
            logger.log_error(f"AI Director: Error during AI decision making: {e}")
        

    def execute_decision(self, decision: dict):
        """
        Executes a decision made by the AI.
        
        Example decision format:
        {
            "type": "spawn_mob",
            "details": { "name": "Vengeful Spirit", "location_dbref": "#123" }
        }
        """
        logger.log_info(f"AI Director: Executing decision: {decision}")
        # Logic to parse and execute the decision would go here.

    def get_game_time(self) -> str:
        """Returns the current in-game time (placeholder)."""
        # In a real scenario, this would connect to a game calendar system.
        return "Day 1, Mid-day"

    # --- Admin and API methods ---

    def get_status(self) -> dict:
        """Returns the current status of the AI Director."""
        return {
            "is_active": self.db.is_active or False,
            "interval": self.interval,
            "last_run": self.db._last_run or "Never",
            "player_count": len(self.db.player_narratives or {}),
            "world_outline_set": bool(self.db.world_story_outline or {})
        }

    def force_run(self):
        """Forces an immediate run of the main loop."""
        self.at_repeat()

    def update_configuration(self, new_config: dict):
        """Updates the script's configuration."""
        self.db.config = new_config
        # The save_story_config method does not exist on AIConfig.
        # Persisting config changes would require modifying ai_config.py directly,
        # which is outside the scope of this fix.
        # if AIConfig:
        #     AIConfig.save_story_config(new_config)
        logger.log_info("AI Director: Configuration updated.")
        
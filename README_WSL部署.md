# 仙侠MUD游戏 - WSL部署指南

## 🎮 项目简介

这是一个基于Evennia框架开发的AI导演驱动仙侠MUD游戏，具有以下特色：

- **🎭 AI导演系统**：智能剧情规划和内容生成
- **⚡ 事件驱动架构**：高性能事件总线系统  
- **🔧 Handler生态**：组件化修仙功能模块
- **🌐 现代化界面**：Web客户端和REST API
- **📊 性能优化**：TagProperty高速查询系统

## 🚀 快速开始

### 方法一：一键启动（推荐）

#### Windows用户
```cmd
# 双击运行或在命令行执行
wsl_quick_start.bat
```

#### Linux/WSL用户
```bash
# 给脚本执行权限
chmod +x wsl_quick_start.sh

# 运行启动脚本
./wsl_quick_start.sh
```

### 方法二：手动部署

详细步骤请参考 `WSL启动调试指南.md`

## 📋 系统要求

### 基础环境
- **操作系统**: Windows 10/11 + WSL2 或 Linux
- **Python**: 3.10-3.12 (推荐3.12)
- **数据库**: PostgreSQL 13+
- **缓存**: Redis 6.0+
- **内存**: 最低1GB，推荐2GB+

### WSL配置
```bash
# 检查WSL版本
wsl --version

# 推荐使用Ubuntu 20.04+
wsl --install -d Ubuntu-20.04
```

## 🛠️ 安装步骤

### 1. 环境准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要软件
sudo apt install python3.12 python3.12-venv python3.12-dev python3-pip -y
sudo apt install postgresql postgresql-contrib redis-server -y
sudo apt install build-essential libpq-dev git -y
```

### 2. 数据库配置
```bash
# 启动服务
sudo service postgresql start
sudo service redis-server start

# 创建数据库
sudo -u postgres createuser -D -A -P xiuxian_user
sudo -u postgres createdb -O xiuxian_user xiuxian_mud
```

### 3. 项目部署
```bash
# 创建虚拟环境
python3.12 -m venv venv
source venv/bin/activate

# 安装依赖
pip install evennia psycopg2-binary django-redis requests

# 进入项目目录
cd xiuxian_mud_new

# 数据库迁移
evennia migrate

# 启动服务器
evennia start
```

## 🎯 功能测试

### 基础测试
```bash
# 运行完整系统测试
python 测试/complete_system_test.py

# 运行功能演示
python 测试/功能演示.py

# 运行AI导演测试
python 测试/ai_director_standalone_test.py

# 运行兼容性检查
python 测试/python312_compatibility_check.py
```

### 游戏内测试
```bash
# 连接游戏
telnet localhost 4000

# 游戏内命令
aidirector status          # 查看AI导演状态
aidirector story <故事>    # 解析故事大纲
aidirector event <事件>    # 触发AI决策
aidirector stats           # 查看性能统计
storydemo                  # 运行故事演示
```

### Web界面测试
- **主页**: http://localhost:4001
- **管理后台**: http://localhost:4001/admin/
- **API文档**: http://localhost:4001/api/

## 🔧 核心功能

### AI导演系统
- **故事大纲解析**：智能分析故事结构和要素
- **实时决策生成**：基于游戏事件生成剧情响应
- **性能监控**：实时统计和缓存优化

### 事件系统
- **高性能事件总线**：毫秒级事件处理
- **优先级队列**：智能事件调度
- **历史记录**：完整的事件追踪

### Handler生态
- **组件化设计**：模块化功能管理
- **依赖管理**：智能依赖解析
- **内存优化**：懒加载和缓存机制

## 📊 性能指标

### 基准测试结果
- **AI响应时间**: < 50ms (平均)
- **事件处理**: < 10ms (单个事件)
- **缓存命中率**: > 50%
- **并发支持**: 100+ 用户

### 系统资源
- **内存使用**: < 500MB (基础运行)
- **CPU占用**: < 20% (正常负载)
- **磁盘空间**: < 200MB (基础安装)

## 🐛 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查服务状态
sudo service postgresql status
sudo service redis-server status

# 重启服务
sudo service postgresql restart
sudo service redis-server restart
```

#### 2. 端口占用
```bash
# 检查端口
netstat -tulpn | grep :4000
netstat -tulpn | grep :4001

# 停止服务
evennia stop
```

#### 3. Python模块错误
```bash
# 确保虚拟环境激活
source venv/bin/activate

# 重新安装依赖
pip install --force-reinstall evennia
```

### 调试工具
```bash
# 查看日志
tail -f xiuxian_mud_new/server/logs/server.log
tail -f xiuxian_mud_new/server/logs/portal.log

# 运行诊断
python 测试/debug_evennia.py

# 检查系统状态
evennia info
evennia status
```

## 📁 项目结构

```
NewEvennia/
├── xiuxian_mud_new/          # 主游戏项目
│   ├── systems/              # 核心系统
│   │   ├── ai_director.py    # AI导演引擎
│   │   ├── ai_client.py      # AI客户端
│   │   ├── event_system.py   # 事件系统
│   │   └── handler_system.py # Handler系统
│   ├── commands/             # 游戏命令
│   ├── typeclasses/          # 游戏对象类
│   ├── web/                  # Web界面
│   └── server/               # 服务器配置
├── mygame/                   # 测试项目
├── 测试/                     # 测试脚本
│   ├── complete_system_test.py
│   ├── 功能演示.py
│   └── ai_director_standalone_test.py
├── WSL启动调试指南.md        # 详细部署指南
├── wsl_quick_start.sh        # Linux启动脚本
├── wsl_quick_start.bat       # Windows启动脚本
└── ai_config.py              # AI配置文件
```

## 🎯 开发指南

### 添加新功能
1. 在 `systems/` 目录下创建新模块
2. 使用事件系统进行模块间通信
3. 通过Handler系统管理组件生命周期
4. 编写相应的测试用例

### 配置AI服务
1. 编辑 `ai_config.py` 文件
2. 设置API密钥和模型参数
3. 重启服务器使配置生效

### 自定义剧情
1. 使用 `aidirector story` 命令解析故事大纲
2. 通过事件系统触发剧情节点
3. AI导演自动生成相应内容

## 📞 技术支持

### 获取帮助
1. 查看 `WSL启动调试指南.md` 详细文档
2. 运行 `python 测试/debug_evennia.py` 诊断问题
3. 检查 `server/logs/` 目录下的日志文件
4. 参考 `测试/` 目录下的测试报告

### 联系方式
- 项目文档：查看项目根目录的Markdown文件
- 测试报告：参考 `测试/` 目录下的详细报告
- 配置示例：查看 `仙侠MUD游戏最终系统架构.md`

## 🎉 开始游戏

部署完成后，您可以：

1. **Web界面体验**：访问 http://localhost:4001
2. **Telnet连接**：`telnet localhost 4000`
3. **创建管理员**：`evennia createsuperuser`
4. **运行演示**：`python 测试/功能演示.py`

---

**祝您游戏开发顺利！** 🎮✨

*本项目展示了AI驱动的现代MUD游戏开发技术，结合了传统MUD的深度玩法和现代AI的智能体验。*

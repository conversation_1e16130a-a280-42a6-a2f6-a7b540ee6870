/*
 *
 */

#clientwrapper #main {
  height: 100%;
}

#clientwrapper #main #main-sub {
  height: 100%;
}

#toolbar {
    display: none;
}

#optionsbutton {
    font-size: .9rem;
    width: .9rem;
    vertical-align: top;
}

.content {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

body .lm_popout {
  display: none;
}

label {
    display: block;
}

.lm_header .lm_tab {
    padding-right: 6px;
    padding-left: 3px;
    padding-bottom: 5px;
    padding-top: 0px;
    margin-top: 1px;
    margin-right: 5px;
}

.lm_title {
    text-align: center;
    margin-right: 2px;
    padding-right: 8px;
}

.lm_tab.lm_active {
    padding-bottom: 5px;
    padding-top: 0px;
    margin-top: 1px;
}
.lm_close_tab {
    opacity: 1 !important;
    background-image: none !important;
    top: 0px !important; 
    right: 0px !important;
}

.lm_close_tab:before {
    font-size: 1.15em;
    width: 1.15em;
    content: "\2715";
}

#typelist {
    position: relative;
}

.typelistsub {
    position: absolute;
    top: 5px;
    left: 5px;
    width: max-content;
    background-color: #333333;
    line-height: .5em;
    padding: .3em;
    font-size: .9em;
    z-index: 1;
}

#renamebox {
    position: relative;
}

#renameboxin {
    position: absolute;
    z-index: 1;
}

#updatelist {
    position: relative;
}

.updatelistsub {
    position: absolute;
    top: 5px;
    left: 5px;
    width: max-content;
    background-color: #333333;
    line-height: .5em;
    padding: .3em;
    font-size: .9em;
    z-index: 1;
}

#inputcontrol {
    height: 100%;
}

.inputwrap {
    position: relative;
    height: 100%;
}

.inputfieldwrapper {
    width: 100%;
    height: 100%;
}

.inputsend {
    position: absolute;
    right: 0;
    z-index: 1;
    height: 100%;
    width: 30px;
    color: white;
    background-color: black;
    border: 0px;
}

.inputfield {
    height: 100%;
    width: calc(100% - 30px);
    background-color: black;
    color: white;
}

.inputfield:focus {
    background-color: black;
    color: white;
}

.glbutton {
    font-size: 0.6em;
    line-height: 1.3em;
    padding: .5em;
}

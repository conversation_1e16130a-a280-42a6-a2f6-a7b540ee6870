# 仙侠MUD游戏 - WSL启动调试完整指南
注意 wsl的系统密码是： zy123good

## 📋 项目概述

这是一个基于Evennia框架的AI导演驱动仙侠MUD游戏，具有以下核心特性：
- **AI导演系统**：智能剧情规划和内容生成
- **事件驱动架构**：高性能事件总线系统
- **Handler生态系统**：组件化修仙功能模块
- **Web界面集成**：现代化Web客户端和API

## 🛠️ WSL环境准备

### 1. 系统要求
```bash
# 检查WSL版本
wsl --version

# 推荐使用Ubuntu 20.04+
lsb_release -a
```



### 2. 安装必要软件
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Python 3.12 (推荐版本)
sudo apt install python3.12 python3.12-venv python3.12-dev python3-pip -y

# 安装PostgreSQL
sudo apt install postgresql postgresql-contrib -y

# 安装Redis
sudo apt install redis-server -y

# 安装Git (如果未安装)
sudo apt install git -y

# 安装其他依赖
sudo apt install build-essential libpq-dev -y
```

### 3. 配置数据库服务
```bash
# 启动PostgreSQL
sudo service postgresql start

# 设置PostgreSQL用户
sudo -u postgres psql
```

在PostgreSQL命令行中执行：
```sql
-- 创建数据库用户
CREATE USER xiuxian_user WITH PASSWORD 'xiuxian_password';

-- 创建数据库
CREATE DATABASE xiuxian_mud OWNER xiuxian_user;

-- 授权
GRANT ALL PRIVILEGES ON DATABASE xiuxian_mud TO xiuxian_user;

-- 退出
\q
```

```bash
# 启动Redis
sudo service redis-server start

# 验证Redis运行
redis-cli ping
```

## 🚀 项目部署步骤

### 1. 克隆项目
```bash
# 进入工作目录
cd ~

# 克隆项目 (假设您已经有项目代码)
# git clone <your-repo-url> NewEvennia
# cd NewEvennia

# 或者直接使用现有项目目录
cd /path/to/your/NewEvennia
```

### 2. 创建Python虚拟环境
```bash
# 创建虚拟环境
python3.12 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 升级pip
pip install --upgrade pip
```

### 3. 安装Python依赖
```bash
# 安装核心依赖
pip install evennia
pip install psycopg2-binary
pip install django-redis
pip install requests
pip install openai  # 如果使用OpenAI API

# 验证安装
python -c "import evennia; print(f'Evennia版本: {evennia.__version__}')"
```

## 🎮 游戏项目配置

### 1. 选择游戏项目
项目中有两个主要的游戏目录：
- `xiuxian_mud_new/` - 新版仙侠MUD项目
- `mygame/` - 测试和开发项目

推荐使用 `xiuxian_mud_new` 作为主要项目：

```bash
cd xiuxian_mud_new
```

### 2. 配置数据库连接
创建 `server/conf/secret_settings.py`：

```bash
cat > server/conf/secret_settings.py << 'EOF'
"""
仙侠MUD游戏密钥配置文件
"""

# 数据库配置
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'xiuxian_mud',
        'USER': 'xiuxian_user',
        'PASSWORD': 'xiuxian_password',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}

# Redis缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://localhost:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Django密钥 (生产环境请更换)
SECRET_KEY = 'xiuxian-mud-secret-key-change-in-production'

# 调试模式
DEBUG = True

# 允许的主机
ALLOWED_HOSTS = ['localhost', '127.0.0.1', '*']
EOF
```

### 3. 配置AI服务
编辑根目录的 `ai_config.py`：

```bash
# 查看当前AI配置
cat ai_config.py

# 如需要，更新API密钥
# 编辑 MINDCRAFT_API_KEY 为您的实际API密钥
```

### 4. 初始化数据库
```bash
# 数据库迁移
evennia migrate

# 创建超级用户
evennia createsuperuser
```

## 🔧 启动和测试

### 1. 启动游戏服务器
```bash
# 启动Evennia服务器
evennia start

# 检查服务器状态
evennia status
```

预期输出：
```
xiuxian_mud_new Portal 4.5.0
  external ports:
    telnet: 4000
    webserver-proxy: 4001
    webclient-websocket: 4002
```

### 2. 验证服务运行
```bash
# 测试Telnet连接
telnet localhost 4000

# 测试Web界面 (在浏览器中访问)
# http://localhost:4001

# 测试API端点
curl http://localhost:4001/api/
```

## 🧪 功能测试指南

### 1. 基础系统测试
```bash
# 运行Python兼容性检查
python 测试/python312_compatibility_check.py

# 运行AI导演系统测试
python 测试/ai_director_standalone_test.py

# 运行简单系统测试
python 测试/simple_test.py
```

### 2. AI导演系统测试
在游戏内使用以下命令：

```
# 连接到游戏
telnet localhost 4000

# 创建角色并登录后执行：

# 查看AI导演状态
aidirector status

# 解析故事大纲
aidirector story 《逆天改命》主角林逸风，废灵根逆天修仙...

# 触发AI事件决策
aidirector event 玩家开始修炼九转玄功

# 查看性能统计
aidirector stats

# 运行故事演示
storydemo
```

### 3. Web界面测试
访问以下URL进行功能测试：

```
# 主页
http://localhost:4001/

# 管理后台
http://localhost:4001/admin/

# API文档
http://localhost:4001/api/

# AI导演API
http://localhost:4001/api/xiuxian/ai-director/story-status/
```

### 4. 事件系统测试
```bash
# 运行事件系统测试
python -c "
import os
os.chdir('xiuxian_mud_new')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
import django
django.setup()

from systems.event_system import XianxiaEventBus, BaseEvent
bus = XianxiaEventBus.get_instance()
event = BaseEvent(event_type='test', data={'message': 'Hello World'})
bus.publish_event(event)
print('事件系统测试完成')
"
```

## 🐛 调试和故障排除

### 1. 常见问题及解决方案

#### 问题1: 数据库连接失败
```bash
# 检查PostgreSQL状态
sudo service postgresql status

# 重启PostgreSQL
sudo service postgresql restart

# 检查数据库连接
psql -h localhost -U xiuxian_user -d xiuxian_mud
```

#### 问题2: Redis连接失败
```bash
# 检查Redis状态
sudo service redis-server status

# 重启Redis
sudo service redis-server restart

# 测试Redis连接
redis-cli ping
```

#### 问题3: Python模块导入错误
```bash
# 确保虚拟环境激活
source venv/bin/activate

# 重新安装依赖
pip install --force-reinstall evennia

# 检查Python路径
python -c "import sys; print('\n'.join(sys.path))"
```

#### 问题4: 端口占用
```bash
# 检查端口占用
netstat -tulpn | grep :4000
netstat -tulpn | grep :4001

# 停止Evennia服务
evennia stop

# 强制杀死进程 (如果需要)
pkill -f evennia
```

### 2. 调试工具

#### 启用详细日志
```bash
# 查看Evennia日志
tail -f server/logs/server.log
tail -f server/logs/portal.log

# 查看Django日志
tail -f server/logs/django.log
```

#### 使用调试脚本
```bash
# 运行Evennia调试脚本
python 测试/debug_evennia.py

# 检查系统状态
evennia info
```

### 3. 性能监控
```bash
# 监控系统资源
htop

# 监控数据库连接
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity WHERE datname='xiuxian_mud';"

# 监控Redis状态
redis-cli info
```

## 📊 功能验证清单

### ✅ 基础环境
- [ ] WSL环境正常运行
- [ ] Python 3.12安装成功
- [ ] PostgreSQL服务运行
- [ ] Redis服务运行
- [ ] 虚拟环境创建并激活

### ✅ 项目配置
- [ ] 项目代码完整
- [ ] 依赖包安装成功
- [ ] 数据库配置正确
- [ ] AI配置文件存在

### ✅ 服务启动
- [ ] 数据库迁移成功
- [ ] 超级用户创建成功
- [ ] Evennia服务启动
- [ ] 端口监听正常

### ✅ 功能测试
- [ ] Telnet连接成功
- [ ] Web界面可访问
- [ ] AI导演命令响应
- [ ] API端点正常
- [ ] 事件系统工作

### ✅ 高级功能
- [ ] 故事大纲解析
- [ ] AI决策生成
- [ ] 性能统计显示
- [ ] Handler系统集成
- [ ] Web API响应

## 🎯 下一步开发建议

1. **完善AI配置**：配置真实的AI API密钥以启用完整功能
2. **扩展游戏内容**：添加更多修仙相关的游戏机制
3. **优化性能**：根据测试结果调优系统性能
4. **增加测试**：编写更多自动化测试用例
5. **部署生产**：准备生产环境部署配置

## 🔍 深度调试指南

### 1. AI导演系统调试

#### 测试AI导演核心功能
```bash
# 进入项目目录
cd xiuxian_mud_new

# 运行AI导演独立测试
python ../测试/ai_director_standalone_test.py
```

预期输出：
```
🚀 开始AI导演系统测试

========================================
测试1: 故事大纲解析
========================================
🔍 开始解析故事大纲...
📖 解析结果:
  标题: 逆天改命
  主题: 凡人逆天修仙，挑战命运束缚
  核心冲突: 废灵根与天道的对抗
  关键角色: 林逸风, 苏清雪, 魔君血煞
  剧情点数: 4
  故事阶段: ['序章', '起承', '转折', '高潮', '结局']

========================================
测试2: AI决策生成
========================================
🎮 事件1: 突破
  决策: 天地灵气震荡，预示着重大事件即将发生。此子前途不可限量...
  响应时间: 50.8ms
  置信度: 0.85

🎉 所有测试通过!
AI导演系统工作正常，满足性能要求
```

#### 调试AI客户端连接
```bash
# 测试AI客户端配置
python -c "
import sys
sys.path.append('xiuxian_mud_new')
from systems.ai_client import get_ai_client

try:
    client = get_ai_client()
    print('✅ AI客户端初始化成功')
    print(f'API Base: {client.base_url}')
    print(f'Model: {client.model}')
except Exception as e:
    print(f'❌ AI客户端初始化失败: {e}')
"
```

### 2. 事件系统调试

#### 测试事件总线
```bash
# 运行事件系统测试
python -c "
import os, sys
sys.path.append('xiuxian_mud_new')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')

try:
    import django
    django.setup()
    print('✅ Django环境设置成功')

    from systems.event_system import XianxiaEventBus, BaseEvent, EventPriority

    # 获取事件总线实例
    bus = XianxiaEventBus.get_instance()
    print('✅ 事件总线获取成功')

    # 创建测试事件
    event = BaseEvent(
        event_type='cultivation_breakthrough',
        data={'player': 'test_player', 'level': 'foundation'},
        priority=EventPriority.HIGH
    )

    # 发布事件
    success = bus.publish_event(event)
    print(f'✅ 事件发布{"成功" if success else "失败"}')

    # 检查事件历史
    history = bus.get_event_history(limit=5)
    print(f'✅ 事件历史记录: {len(history)} 条')

except Exception as e:
    print(f'❌ 事件系统测试失败: {e}')
    import traceback
    traceback.print_exc()
"
```

### 3. Handler系统调试

#### 测试Handler生态系统
```bash
# 运行Handler系统测试
python ../xiuxian_test_gmcp.py
```

这将测试以下Handler：
- 修炼系统Handler
- 战斗技能Handler
- 炼丹系统Handler
- 因果系统Handler
- AI导演Handler

#### 检查Handler依赖关系
```bash
python -c "
import sys
sys.path.append('xiuxian_mud_new')
from systems.handler_system import HandlerRegistry

# 获取依赖关系图
graph = HandlerRegistry.get_dependency_graph()
print('Handler依赖关系:')
for handler, deps in graph['dependencies'].items():
    print(f'  {handler} -> {deps}')
"
```

### 4. Web API调试

#### 测试API端点
```bash
# 测试基础API
curl -X GET http://localhost:4001/api/ \
  -H "Content-Type: application/json"

# 测试AI导演API
curl -X GET http://localhost:4001/api/xiuxian/ai-director/story-status/ \
  -H "Content-Type: application/json"

# 测试世界状态API
curl -X GET http://localhost:4001/api/xiuxian/ai-director/world-state/ \
  -H "Content-Type: application/json"

# 测试上下文更新API
curl -X POST http://localhost:4001/api/xiuxian/ai-director/update-context/ \
  -H "Content-Type: application/json" \
  -d '{"context": "玩家开始修炼", "player": "test_player"}'
```

### 5. 数据库调试

#### 检查数据库表结构
```bash
# 连接数据库
psql -h localhost -U xiuxian_user -d xiuxian_mud

# 在psql中执行：
\dt  -- 查看所有表
\d objects_objectdb  -- 查看对象表结构
\d accounts_accountdb  -- 查看账户表结构

# 查看数据统计
SELECT COUNT(*) FROM objects_objectdb;
SELECT COUNT(*) FROM accounts_accountdb;
SELECT COUNT(*) FROM scripts_scriptdb;

\q  -- 退出
```

#### 检查Redis缓存
```bash
# 连接Redis
redis-cli

# 在redis-cli中执行：
INFO  # 查看Redis信息
KEYS *  # 查看所有键
GET evennia:*  # 查看Evennia相关缓存

exit  # 退出
```

### 6. 性能调试

#### 运行性能基准测试
```bash
# 运行TagProperty性能测试
cd mygame
python systems/tagproperty_performance_test.py
```

#### 监控系统资源
```bash
# 实时监控系统资源
htop

# 监控网络连接
netstat -tulpn | grep evennia

# 监控磁盘使用
df -h

# 监控内存使用
free -h
```

### 7. 日志分析

#### 查看实时日志
```bash
# 查看服务器日志
tail -f xiuxian_mud_new/server/logs/server.log

# 查看门户日志
tail -f xiuxian_mud_new/server/logs/portal.log

# 查看错误日志
grep -i error xiuxian_mud_new/server/logs/*.log

# 查看AI相关日志
grep -i "ai\|director" xiuxian_mud_new/server/logs/*.log
```

#### 日志级别配置
在 `server/conf/settings.py` 中添加：
```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'DEBUG',
            'class': 'logging.FileHandler',
            'filename': 'server/logs/debug.log',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'systems.ai_director': {
            'handlers': ['file'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'systems.event_system': {
            'handlers': ['file'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}
```

## 🚨 故障排除手册

### 常见错误及解决方案

#### 1. 模块导入错误
```
ModuleNotFoundError: No module named 'systems'
```
**解决方案**：
```bash
# 确保在正确的目录
cd xiuxian_mud_new

# 检查Python路径
python -c "import sys; print(sys.path)"

# 设置PYTHONPATH
export PYTHONPATH=$PWD:$PYTHONPATH
```

#### 2. 数据库迁移失败
```
django.db.utils.OperationalError: could not connect to server
```
**解决方案**：
```bash
# 检查PostgreSQL状态
sudo service postgresql status

# 重启PostgreSQL
sudo service postgresql restart

# 检查配置文件
cat server/conf/secret_settings.py

# 测试数据库连接
python manage.py dbshell
```

#### 3. AI API连接失败
```
ValueError: 未配置有效的MINDCRAFT_API_KEY
```
**解决方案**：
```bash
# 编辑AI配置文件
nano ai_config.py

# 更新API密钥
MINDCRAFT_API_KEY = "your-actual-api-key-here"

# 或使用环境变量
export MINDCRAFT_API_KEY="your-actual-api-key-here"
```

#### 4. 端口冲突
```
OSError: [Errno 98] Address already in use
```
**解决方案**：
```bash
# 查找占用端口的进程
lsof -i :4000
lsof -i :4001

# 杀死进程
kill -9 <PID>

# 或更改端口配置
# 在settings.py中修改TELNET_PORTS和WEBSERVER_PORTS
```

#### 5. 权限问题
```
PermissionError: [Errno 13] Permission denied
```
**解决方案**：
```bash
# 检查文件权限
ls -la server/logs/

# 修复权限
chmod 755 server/logs/
chmod 644 server/logs/*.log

# 确保虚拟环境权限
chown -R $USER:$USER venv/
```

## 📊 测试报告模板

### 功能测试报告
创建测试报告文件：
```bash
cat > 测试报告_$(date +%Y%m%d).md << 'EOF'
# 仙侠MUD功能测试报告

## 测试环境
- 操作系统: Ubuntu 20.04 (WSL)
- Python版本: 3.12.x
- Evennia版本: 4.5.0
- 测试时间: $(date)

## 测试结果

### ✅ 基础功能测试
- [ ] 服务器启动
- [ ] 数据库连接
- [ ] Redis缓存
- [ ] Web界面访问
- [ ] Telnet连接

### ✅ AI导演系统测试
- [ ] 故事大纲解析
- [ ] AI决策生成
- [ ] 性能基准测试
- [ ] API端点响应

### ✅ 事件系统测试
- [ ] 事件发布
- [ ] 事件处理
- [ ] 事件历史
- [ ] 性能监控

### ✅ Handler系统测试
- [ ] 修炼系统
- [ ] 战斗系统
- [ ] 炼丹系统
- [ ] 因果系统

## 性能指标
- 平均响应时间: __ms
- 内存使用: __MB
- CPU使用率: __%
- 并发用户数: __

## 问题记录
1.
2.
3.

## 改进建议
1.
2.
3.
EOF
```

## 📞 技术支持

### 获取帮助的步骤
1. **检查日志**：查看 `server/logs/` 目录下的日志文件
2. **运行诊断**：执行 `测试/debug_evennia.py` 诊断脚本
3. **查看文档**：参考项目中的 `测试/` 目录下的测试报告
4. **检查配置**：确认所有配置文件设置正确
5. **验证服务**：确保PostgreSQL和Redis服务正常运行

### 联系信息
- 项目文档：查看项目根目录的Markdown文件
- 测试报告：参考 `测试/` 目录下的详细报告
- 配置示例：查看 `仙侠MUD游戏最终系统架构.md`

---

**祝您游戏开发顺利！** 🎮✨

*本指南涵盖了从环境搭建到深度调试的完整流程，帮助您快速上手并解决可能遇到的问题。*

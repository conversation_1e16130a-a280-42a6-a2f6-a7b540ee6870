|Portal| 2025-06-30 13:16:00 [..] Loaded.
|Portal| 2025-06-30 13:15:58 [..] Loading /home/<USER>/.local/lib/python3.12/site-packages/evennia/server/portal/portal.py...
|Portal| 2025-06-30 13:15:59 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:15:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
|Portal| 2025-06-30 13:15:59 [!!]     return storage_object[key]
|Portal| 2025-06-30 13:15:59 [!!]            ~~~~~~~~~~~~~~^^^^^
|Portal| 2025-06-30 13:15:59 [!!] KeyError: 'throttle'
|Portal| None|Portal| 2025-06-30 13:15:59 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:15:59 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:15:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
|Portal| 2025-06-30 13:15:59 [!!]     return getattr(self._connections, alias)
|Portal| 2025-06-30 13:15:59 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:15:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
|Portal| 2025-06-30 13:15:59 [!!]     return getattr(storage, key)
|Portal| 2025-06-30 13:15:59 [!!]            ^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:15:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
|Portal| 2025-06-30 13:15:59 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
|Portal| 2025-06-30 13:15:59 [!!] AttributeError: <asgiref.local._CVar object at 0x7e9d78ef7260> object has no attribute 'throttle'
|Portal| None|Portal| 2025-06-30 13:15:59 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:15:59 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:15:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
|Portal| 2025-06-30 13:15:59 [!!]     self.storage = caches["throttle"]
|Portal| 2025-06-30 13:15:59 [!!]                    ~~~~~~^^^^^^^^^^^^
|Portal| 2025-06-30 13:15:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
|Portal| 2025-06-30 13:15:59 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
|Portal| 2025-06-30 13:15:59 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
|Portal| 2025-06-30 13:15:59 [!!] Throttle: Errors encountered; using default cache.
|Portal| 2025-06-30 13:15:59 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:15:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
|Portal| 2025-06-30 13:15:59 [!!]     return storage_object[key]
|Portal| 2025-06-30 13:15:59 [!!]            ~~~~~~~~~~~~~~^^^^^
|Portal| 2025-06-30 13:15:59 [!!] KeyError: 'throttle'
|Portal| None|Portal| 2025-06-30 13:15:59 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:15:59 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:15:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
|Portal| 2025-06-30 13:15:59 [!!]     return getattr(self._connections, alias)
|Portal| 2025-06-30 13:15:59 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:15:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
|Portal| 2025-06-30 13:15:59 [!!]     return getattr(storage, key)
|Portal| 2025-06-30 13:15:59 [!!]            ^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:15:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
|Portal| 2025-06-30 13:15:59 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
|Portal| 2025-06-30 13:15:59 [!!] AttributeError: <asgiref.local._CVar object at 0x7e9d78ef7260> object has no attribute 'throttle'
|Portal| None|Portal| 2025-06-30 13:15:59 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:15:59 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:15:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
|Portal| 2025-06-30 13:15:59 [!!]     self.storage = caches["throttle"]
|Portal| 2025-06-30 13:15:59 [!!]                    ~~~~~~^^^^^^^^^^^^
|Portal| 2025-06-30 13:15:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
|Portal| 2025-06-30 13:15:59 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
|Portal| 2025-06-30 13:15:59 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
|Portal| 2025-06-30 13:15:59 [!!] Throttle: Errors encountered; using default cache.
|Portal| 2025-06-30 13:16:00 [..] Loaded.
|Portal| 2025-06-30 13:16:00 [..] twistd 23.10.0 (/usr/bin/python3.12 3.12.3) starting up.
|Portal| 2025-06-30 13:16:00 [..] reactor class: twisted.internet.epollreactor.EPollReactor.
|Portal| 2025-06-30 13:16:00 [..] AMP starting on 4006
|Portal| 2025-06-30 13:16:00 [..] Telnet starting on 4000
|Portal| 2025-06-30 13:16:00 [..] Websocket starting on 4002
|Portal| 2025-06-30 13:16:00 [..] Webserver-proxy starting on 4001
|Portal| 2025-06-30 13:16:01 [..] Portal starting server ... 
|Portal| 2025-06-30 13:16:05 [..] (TCP Port 4001 Closed)
|Portal| 2025-06-30 13:16:05 [..] (TCP Port 4002 Closed)
|Portal| 2025-06-30 13:16:05 [..] (TCP Port 4000 Closed)
|Portal| 2025-06-30 13:16:05 [..] (TCP Port 4006 Closed)
|Portal| 2025-06-30 13:16:05 [..] Main loop terminated.
|Portal| 2025-06-30 13:16:05 [..] Server Shut Down.
|Portal| 2025-06-30 13:30:50 [..] Loaded.
|Portal| 2025-06-30 13:30:49 [..] Loading /home/<USER>/.local/lib/python3.12/site-packages/evennia/server/portal/portal.py...
|Portal| 2025-06-30 13:30:49 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:30:49 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
|Portal| 2025-06-30 13:30:49 [!!]     return storage_object[key]
|Portal| 2025-06-30 13:30:49 [!!]            ~~~~~~~~~~~~~~^^^^^
|Portal| 2025-06-30 13:30:49 [!!] KeyError: 'throttle'
|Portal| None|Portal| 2025-06-30 13:30:49 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:30:49 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:30:49 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
|Portal| 2025-06-30 13:30:49 [!!]     return getattr(self._connections, alias)
|Portal| 2025-06-30 13:30:49 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:30:49 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
|Portal| 2025-06-30 13:30:49 [!!]     return getattr(storage, key)
|Portal| 2025-06-30 13:30:49 [!!]            ^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:30:49 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
|Portal| 2025-06-30 13:30:49 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
|Portal| 2025-06-30 13:30:49 [!!] AttributeError: <asgiref.local._CVar object at 0x714d2f9db7a0> object has no attribute 'throttle'
|Portal| None|Portal| 2025-06-30 13:30:49 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:30:49 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:30:49 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
|Portal| 2025-06-30 13:30:49 [!!]     self.storage = caches["throttle"]
|Portal| 2025-06-30 13:30:49 [!!]                    ~~~~~~^^^^^^^^^^^^
|Portal| 2025-06-30 13:30:49 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
|Portal| 2025-06-30 13:30:49 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
|Portal| 2025-06-30 13:30:49 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
|Portal| 2025-06-30 13:30:49 [!!] Throttle: Errors encountered; using default cache.
|Portal| 2025-06-30 13:30:49 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:30:49 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
|Portal| 2025-06-30 13:30:49 [!!]     return storage_object[key]
|Portal| 2025-06-30 13:30:49 [!!]            ~~~~~~~~~~~~~~^^^^^
|Portal| 2025-06-30 13:30:49 [!!] KeyError: 'throttle'
|Portal| None|Portal| 2025-06-30 13:30:49 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:30:49 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:30:49 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
|Portal| 2025-06-30 13:30:49 [!!]     return getattr(self._connections, alias)
|Portal| 2025-06-30 13:30:49 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:30:49 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
|Portal| 2025-06-30 13:30:49 [!!]     return getattr(storage, key)
|Portal| 2025-06-30 13:30:49 [!!]            ^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:30:49 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
|Portal| 2025-06-30 13:30:49 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
|Portal| 2025-06-30 13:30:49 [!!] AttributeError: <asgiref.local._CVar object at 0x714d2f9db7a0> object has no attribute 'throttle'
|Portal| None|Portal| 2025-06-30 13:30:49 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:30:49 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:30:49 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
|Portal| 2025-06-30 13:30:49 [!!]     self.storage = caches["throttle"]
|Portal| 2025-06-30 13:30:49 [!!]                    ~~~~~~^^^^^^^^^^^^
|Portal| 2025-06-30 13:30:49 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
|Portal| 2025-06-30 13:30:49 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
|Portal| 2025-06-30 13:30:49 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
|Portal| 2025-06-30 13:30:49 [!!] Throttle: Errors encountered; using default cache.
|Portal| 2025-06-30 13:30:50 [..] Loaded.
|Portal| 2025-06-30 13:30:50 [..] twistd 23.10.0 (/usr/bin/python3.12 3.12.3) starting up.
|Portal| 2025-06-30 13:30:50 [..] reactor class: twisted.internet.epollreactor.EPollReactor.
|Portal| 2025-06-30 13:30:50 [..] AMP starting on 4006
|Portal| 2025-06-30 13:30:50 [..] Telnet starting on 4000
|Portal| 2025-06-30 13:30:50 [..] Websocket starting on 4002
|Portal| 2025-06-30 13:30:50 [..] Webserver-proxy starting on 4001
|Portal| 2025-06-30 13:30:51 [..] Portal starting server ... 
|Portal| 2025-06-30 13:30:55 [..] (TCP Port 4001 Closed)
|Portal| 2025-06-30 13:30:55 [..] (TCP Port 4002 Closed)
|Portal| 2025-06-30 13:30:55 [..] (TCP Port 4000 Closed)
|Portal| 2025-06-30 13:30:55 [..] (TCP Port 4006 Closed)
|Portal| 2025-06-30 13:30:55 [..] Main loop terminated.
|Portal| 2025-06-30 13:30:55 [..] Server Shut Down.
|Portal| 2025-06-30 13:33:02 [..] Loaded.
|Portal| 2025-06-30 13:33:00 [..] Loading /home/<USER>/.local/lib/python3.12/site-packages/evennia/server/portal/portal.py...
|Portal| 2025-06-30 13:33:00 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:33:00 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
|Portal| 2025-06-30 13:33:00 [!!]     return storage_object[key]
|Portal| 2025-06-30 13:33:00 [!!]            ~~~~~~~~~~~~~~^^^^^
|Portal| 2025-06-30 13:33:00 [!!] KeyError: 'throttle'
|Portal| None|Portal| 2025-06-30 13:33:00 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:33:00 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:33:00 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
|Portal| 2025-06-30 13:33:00 [!!]     return getattr(self._connections, alias)
|Portal| 2025-06-30 13:33:00 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:33:00 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
|Portal| 2025-06-30 13:33:00 [!!]     return getattr(storage, key)
|Portal| 2025-06-30 13:33:00 [!!]            ^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:33:00 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
|Portal| 2025-06-30 13:33:00 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
|Portal| 2025-06-30 13:33:00 [!!] AttributeError: <asgiref.local._CVar object at 0x78cf3bcff8c0> object has no attribute 'throttle'
|Portal| None|Portal| 2025-06-30 13:33:00 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:33:00 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:33:00 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
|Portal| 2025-06-30 13:33:00 [!!]     self.storage = caches["throttle"]
|Portal| 2025-06-30 13:33:00 [!!]                    ~~~~~~^^^^^^^^^^^^
|Portal| 2025-06-30 13:33:00 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
|Portal| 2025-06-30 13:33:00 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
|Portal| 2025-06-30 13:33:00 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
|Portal| 2025-06-30 13:33:00 [!!] Throttle: Errors encountered; using default cache.
|Portal| 2025-06-30 13:33:00 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:33:00 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
|Portal| 2025-06-30 13:33:00 [!!]     return storage_object[key]
|Portal| 2025-06-30 13:33:00 [!!]            ~~~~~~~~~~~~~~^^^^^
|Portal| 2025-06-30 13:33:00 [!!] KeyError: 'throttle'
|Portal| None|Portal| 2025-06-30 13:33:00 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:33:00 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:33:00 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
|Portal| 2025-06-30 13:33:00 [!!]     return getattr(self._connections, alias)
|Portal| 2025-06-30 13:33:00 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:33:00 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
|Portal| 2025-06-30 13:33:00 [!!]     return getattr(storage, key)
|Portal| 2025-06-30 13:33:00 [!!]            ^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:33:00 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
|Portal| 2025-06-30 13:33:00 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
|Portal| 2025-06-30 13:33:00 [!!] AttributeError: <asgiref.local._CVar object at 0x78cf3bcff8c0> object has no attribute 'throttle'
|Portal| None|Portal| 2025-06-30 13:33:00 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:33:00 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:33:00 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
|Portal| 2025-06-30 13:33:00 [!!]     self.storage = caches["throttle"]
|Portal| 2025-06-30 13:33:00 [!!]                    ~~~~~~^^^^^^^^^^^^
|Portal| 2025-06-30 13:33:00 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
|Portal| 2025-06-30 13:33:00 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
|Portal| 2025-06-30 13:33:00 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
|Portal| 2025-06-30 13:33:00 [!!] Throttle: Errors encountered; using default cache.
|Portal| 2025-06-30 13:33:02 [..] Loaded.
|Portal| 2025-06-30 13:33:02 [..] twistd 23.10.0 (/usr/bin/python3.12 3.12.3) starting up.
|Portal| 2025-06-30 13:33:02 [..] reactor class: twisted.internet.epollreactor.EPollReactor.
|Portal| 2025-06-30 13:33:02 [..] AMP starting on 4006
|Portal| 2025-06-30 13:33:02 [..] Telnet starting on 4000
|Portal| 2025-06-30 13:33:02 [..] Websocket starting on 4002
|Portal| 2025-06-30 13:33:02 [..] Webserver-proxy starting on 4001
|Portal| 2025-06-30 13:33:02 [..] Portal starting server ... 
|Portal| 2025-06-30 13:33:06 [..] (TCP Port 4001 Closed)
|Portal| 2025-06-30 13:33:06 [..] (TCP Port 4002 Closed)
|Portal| 2025-06-30 13:33:06 [..] (TCP Port 4000 Closed)
|Portal| 2025-06-30 13:33:06 [..] (TCP Port 4006 Closed)
|Portal| 2025-06-30 13:33:06 [..] Main loop terminated.
|Portal| 2025-06-30 13:33:06 [..] Server Shut Down.
|Portal| 2025-06-30 13:35:40 [..] Loaded.
|Portal| 2025-06-30 13:35:38 [..] Loading /home/<USER>/.local/lib/python3.12/site-packages/evennia/server/portal/portal.py...
|Portal| 2025-06-30 13:35:38 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:35:38 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
|Portal| 2025-06-30 13:35:38 [!!]     return storage_object[key]
|Portal| 2025-06-30 13:35:38 [!!]            ~~~~~~~~~~~~~~^^^^^
|Portal| 2025-06-30 13:35:38 [!!] KeyError: 'throttle'
|Portal| None|Portal| 2025-06-30 13:35:38 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:35:38 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:35:38 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
|Portal| 2025-06-30 13:35:38 [!!]     return getattr(self._connections, alias)
|Portal| 2025-06-30 13:35:38 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:35:38 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
|Portal| 2025-06-30 13:35:38 [!!]     return getattr(storage, key)
|Portal| 2025-06-30 13:35:38 [!!]            ^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:35:38 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
|Portal| 2025-06-30 13:35:38 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
|Portal| 2025-06-30 13:35:38 [!!] AttributeError: <asgiref.local._CVar object at 0x7b6121beb7a0> object has no attribute 'throttle'
|Portal| None|Portal| 2025-06-30 13:35:38 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:35:38 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:35:38 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
|Portal| 2025-06-30 13:35:38 [!!]     self.storage = caches["throttle"]
|Portal| 2025-06-30 13:35:38 [!!]                    ~~~~~~^^^^^^^^^^^^
|Portal| 2025-06-30 13:35:38 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
|Portal| 2025-06-30 13:35:38 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
|Portal| 2025-06-30 13:35:38 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
|Portal| 2025-06-30 13:35:38 [!!] Throttle: Errors encountered; using default cache.
|Portal| 2025-06-30 13:35:38 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:35:38 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
|Portal| 2025-06-30 13:35:38 [!!]     return storage_object[key]
|Portal| 2025-06-30 13:35:38 [!!]            ~~~~~~~~~~~~~~^^^^^
|Portal| 2025-06-30 13:35:38 [!!] KeyError: 'throttle'
|Portal| None|Portal| 2025-06-30 13:35:38 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:35:38 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:35:38 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
|Portal| 2025-06-30 13:35:38 [!!]     return getattr(self._connections, alias)
|Portal| 2025-06-30 13:35:38 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:35:38 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
|Portal| 2025-06-30 13:35:38 [!!]     return getattr(storage, key)
|Portal| 2025-06-30 13:35:38 [!!]            ^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:35:38 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
|Portal| 2025-06-30 13:35:38 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
|Portal| 2025-06-30 13:35:38 [!!] AttributeError: <asgiref.local._CVar object at 0x7b6121beb7a0> object has no attribute 'throttle'
|Portal| None|Portal| 2025-06-30 13:35:38 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:35:38 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:35:38 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
|Portal| 2025-06-30 13:35:38 [!!]     self.storage = caches["throttle"]
|Portal| 2025-06-30 13:35:38 [!!]                    ~~~~~~^^^^^^^^^^^^
|Portal| 2025-06-30 13:35:38 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
|Portal| 2025-06-30 13:35:38 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
|Portal| 2025-06-30 13:35:38 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
|Portal| 2025-06-30 13:35:38 [!!] Throttle: Errors encountered; using default cache.
|Portal| 2025-06-30 13:35:40 [..] Loaded.
|Portal| 2025-06-30 13:35:40 [..] twistd 23.10.0 (/usr/bin/python3.12 3.12.3) starting up.
|Portal| 2025-06-30 13:35:40 [..] reactor class: twisted.internet.epollreactor.EPollReactor.
|Portal| 2025-06-30 13:35:40 [..] AMP starting on 4006
|Portal| 2025-06-30 13:35:40 [..] Telnet starting on 4000
|Portal| 2025-06-30 13:35:40 [..] Websocket starting on 4002
|Portal| 2025-06-30 13:35:40 [..] Webserver-proxy starting on 4001
|Portal| 2025-06-30 13:35:40 [..] Portal starting server ... 
|Portal| 2025-06-30 13:35:44 [..] (TCP Port 4001 Closed)
|Portal| 2025-06-30 13:35:44 [..] (TCP Port 4002 Closed)
|Portal| 2025-06-30 13:35:44 [..] (TCP Port 4000 Closed)
|Portal| 2025-06-30 13:35:44 [..] (TCP Port 4006 Closed)
|Portal| 2025-06-30 13:35:44 [..] Main loop terminated.
|Portal| 2025-06-30 13:35:44 [..] Server Shut Down.
|Portal| 2025-06-30 13:40:58 [..] Loaded.
|Portal| 2025-06-30 13:40:56 [..] Loading /home/<USER>/.local/lib/python3.12/site-packages/evennia/server/portal/portal.py...
|Portal| 2025-06-30 13:40:56 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:40:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
|Portal| 2025-06-30 13:40:56 [!!]     return storage_object[key]
|Portal| 2025-06-30 13:40:56 [!!]            ~~~~~~~~~~~~~~^^^^^
|Portal| 2025-06-30 13:40:56 [!!] KeyError: 'throttle'
|Portal| None|Portal| 2025-06-30 13:40:56 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:40:56 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:40:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
|Portal| 2025-06-30 13:40:56 [!!]     return getattr(self._connections, alias)
|Portal| 2025-06-30 13:40:56 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:40:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
|Portal| 2025-06-30 13:40:56 [!!]     return getattr(storage, key)
|Portal| 2025-06-30 13:40:56 [!!]            ^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:40:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
|Portal| 2025-06-30 13:40:56 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
|Portal| 2025-06-30 13:40:56 [!!] AttributeError: <asgiref.local._CVar object at 0x72cbce6eb7a0> object has no attribute 'throttle'
|Portal| None|Portal| 2025-06-30 13:40:56 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:40:56 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:40:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
|Portal| 2025-06-30 13:40:56 [!!]     self.storage = caches["throttle"]
|Portal| 2025-06-30 13:40:56 [!!]                    ~~~~~~^^^^^^^^^^^^
|Portal| 2025-06-30 13:40:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
|Portal| 2025-06-30 13:40:56 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
|Portal| 2025-06-30 13:40:56 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
|Portal| 2025-06-30 13:40:56 [!!] Throttle: Errors encountered; using default cache.
|Portal| 2025-06-30 13:40:56 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:40:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
|Portal| 2025-06-30 13:40:56 [!!]     return storage_object[key]
|Portal| 2025-06-30 13:40:56 [!!]            ~~~~~~~~~~~~~~^^^^^
|Portal| 2025-06-30 13:40:56 [!!] KeyError: 'throttle'
|Portal| None|Portal| 2025-06-30 13:40:56 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:40:56 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:40:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
|Portal| 2025-06-30 13:40:56 [!!]     return getattr(self._connections, alias)
|Portal| 2025-06-30 13:40:56 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:40:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
|Portal| 2025-06-30 13:40:56 [!!]     return getattr(storage, key)
|Portal| 2025-06-30 13:40:56 [!!]            ^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:40:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
|Portal| 2025-06-30 13:40:56 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
|Portal| 2025-06-30 13:40:56 [!!] AttributeError: <asgiref.local._CVar object at 0x72cbce6eb7a0> object has no attribute 'throttle'
|Portal| None|Portal| 2025-06-30 13:40:56 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:40:56 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:40:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
|Portal| 2025-06-30 13:40:56 [!!]     self.storage = caches["throttle"]
|Portal| 2025-06-30 13:40:56 [!!]                    ~~~~~~^^^^^^^^^^^^
|Portal| 2025-06-30 13:40:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
|Portal| 2025-06-30 13:40:56 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
|Portal| 2025-06-30 13:40:56 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
|Portal| 2025-06-30 13:40:56 [!!] Throttle: Errors encountered; using default cache.
|Portal| 2025-06-30 13:40:58 [..] Loaded.
|Portal| 2025-06-30 13:40:58 [..] twistd 23.10.0 (/usr/bin/python3.12 3.12.3) starting up.
|Portal| 2025-06-30 13:40:58 [..] reactor class: twisted.internet.epollreactor.EPollReactor.
|Portal| 2025-06-30 13:40:58 [..] AMP starting on 4006
|Portal| 2025-06-30 13:40:58 [..] Telnet starting on 4000
|Portal| 2025-06-30 13:40:58 [..] Websocket starting on 4002
|Portal| 2025-06-30 13:40:58 [..] Webserver-proxy starting on 4001
|Portal| 2025-06-30 13:40:58 [..] Portal starting server ... 
|Portal| 2025-06-30 13:41:02 [..] (TCP Port 4001 Closed)
|Portal| 2025-06-30 13:41:02 [..] (TCP Port 4002 Closed)
|Portal| 2025-06-30 13:41:02 [..] (TCP Port 4000 Closed)
|Portal| 2025-06-30 13:41:02 [..] (TCP Port 4006 Closed)
|Portal| 2025-06-30 13:41:02 [..] Main loop terminated.
|Portal| 2025-06-30 13:41:02 [..] Server Shut Down.
|Portal| 2025-06-30 13:57:03 [..] Loaded.
|Portal| 2025-06-30 13:57:01 [..] Loading /home/<USER>/.local/lib/python3.12/site-packages/evennia/server/portal/portal.py...
|Portal| 2025-06-30 13:57:02 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:57:02 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
|Portal| 2025-06-30 13:57:02 [!!]     return storage_object[key]
|Portal| 2025-06-30 13:57:02 [!!]            ~~~~~~~~~~~~~~^^^^^
|Portal| 2025-06-30 13:57:02 [!!] KeyError: 'throttle'
|Portal| None|Portal| 2025-06-30 13:57:02 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:57:02 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:57:02 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
|Portal| 2025-06-30 13:57:02 [!!]     return getattr(self._connections, alias)
|Portal| 2025-06-30 13:57:02 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:57:02 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
|Portal| 2025-06-30 13:57:02 [!!]     return getattr(storage, key)
|Portal| 2025-06-30 13:57:02 [!!]            ^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:57:02 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
|Portal| 2025-06-30 13:57:02 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
|Portal| 2025-06-30 13:57:02 [!!] AttributeError: <asgiref.local._CVar object at 0x7d1405683650> object has no attribute 'throttle'
|Portal| None|Portal| 2025-06-30 13:57:02 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:57:02 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:57:02 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
|Portal| 2025-06-30 13:57:02 [!!]     self.storage = caches["throttle"]
|Portal| 2025-06-30 13:57:02 [!!]                    ~~~~~~^^^^^^^^^^^^
|Portal| 2025-06-30 13:57:02 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
|Portal| 2025-06-30 13:57:02 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
|Portal| 2025-06-30 13:57:02 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
|Portal| 2025-06-30 13:57:02 [!!] Throttle: Errors encountered; using default cache.
|Portal| 2025-06-30 13:57:02 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:57:02 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
|Portal| 2025-06-30 13:57:02 [!!]     return storage_object[key]
|Portal| 2025-06-30 13:57:02 [!!]            ~~~~~~~~~~~~~~^^^^^
|Portal| 2025-06-30 13:57:02 [!!] KeyError: 'throttle'
|Portal| None|Portal| 2025-06-30 13:57:02 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:57:02 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:57:02 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
|Portal| 2025-06-30 13:57:02 [!!]     return getattr(self._connections, alias)
|Portal| 2025-06-30 13:57:02 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:57:02 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
|Portal| 2025-06-30 13:57:02 [!!]     return getattr(storage, key)
|Portal| 2025-06-30 13:57:02 [!!]            ^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 13:57:02 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
|Portal| 2025-06-30 13:57:02 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
|Portal| 2025-06-30 13:57:02 [!!] AttributeError: <asgiref.local._CVar object at 0x7d1405683650> object has no attribute 'throttle'
|Portal| None|Portal| 2025-06-30 13:57:02 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 13:57:02 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 13:57:02 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
|Portal| 2025-06-30 13:57:02 [!!]     self.storage = caches["throttle"]
|Portal| 2025-06-30 13:57:02 [!!]                    ~~~~~~^^^^^^^^^^^^
|Portal| 2025-06-30 13:57:02 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
|Portal| 2025-06-30 13:57:02 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
|Portal| 2025-06-30 13:57:02 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
|Portal| 2025-06-30 13:57:02 [!!] Throttle: Errors encountered; using default cache.
|Portal| 2025-06-30 13:57:03 [..] Loaded.
|Portal| 2025-06-30 13:57:03 [..] twistd 23.10.0 (/usr/bin/python3.12 3.12.3) starting up.
|Portal| 2025-06-30 13:57:03 [..] reactor class: twisted.internet.epollreactor.EPollReactor.
|Portal| 2025-06-30 13:57:03 [..] AMP starting on 4006
|Portal| 2025-06-30 13:57:03 [..] Telnet starting on 4000
|Portal| 2025-06-30 13:57:03 [..] Websocket starting on 4002
|Portal| 2025-06-30 13:57:03 [..] Webserver-proxy starting on 4001
|Portal| 2025-06-30 13:57:04 [..] Portal starting server ... 
|Portal| 2025-06-30 13:57:08 [..] (TCP Port 4001 Closed)
|Portal| 2025-06-30 13:57:08 [..] (TCP Port 4002 Closed)
|Portal| 2025-06-30 13:57:08 [..] (TCP Port 4000 Closed)
|Portal| 2025-06-30 13:57:08 [..] (TCP Port 4006 Closed)
|Portal| 2025-06-30 13:57:08 [..] Main loop terminated.
|Portal| 2025-06-30 13:57:08 [..] Server Shut Down.
|Portal| 2025-06-30 14:22:08 [..] Loaded.
|Portal| 2025-06-30 14:22:07 [..] Loading /home/<USER>/.local/lib/python3.12/site-packages/evennia/server/portal/portal.py...
|Portal| 2025-06-30 14:22:07 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 14:22:07 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
|Portal| 2025-06-30 14:22:07 [!!]     return storage_object[key]
|Portal| 2025-06-30 14:22:07 [!!]            ~~~~~~~~~~~~~~^^^^^
|Portal| 2025-06-30 14:22:07 [!!] KeyError: 'throttle'
|Portal| None|Portal| 2025-06-30 14:22:07 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 14:22:07 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 14:22:07 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
|Portal| 2025-06-30 14:22:07 [!!]     return getattr(self._connections, alias)
|Portal| 2025-06-30 14:22:07 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 14:22:07 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
|Portal| 2025-06-30 14:22:07 [!!]     return getattr(storage, key)
|Portal| 2025-06-30 14:22:07 [!!]            ^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 14:22:07 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
|Portal| 2025-06-30 14:22:07 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
|Portal| 2025-06-30 14:22:07 [!!] AttributeError: <asgiref.local._CVar object at 0x7b86a345f650> object has no attribute 'throttle'
|Portal| None|Portal| 2025-06-30 14:22:07 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 14:22:07 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 14:22:07 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
|Portal| 2025-06-30 14:22:07 [!!]     self.storage = caches["throttle"]
|Portal| 2025-06-30 14:22:07 [!!]                    ~~~~~~^^^^^^^^^^^^
|Portal| 2025-06-30 14:22:07 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
|Portal| 2025-06-30 14:22:07 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
|Portal| 2025-06-30 14:22:07 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
|Portal| 2025-06-30 14:22:07 [!!] Throttle: Errors encountered; using default cache.
|Portal| 2025-06-30 14:22:07 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 14:22:07 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
|Portal| 2025-06-30 14:22:07 [!!]     return storage_object[key]
|Portal| 2025-06-30 14:22:07 [!!]            ~~~~~~~~~~~~~~^^^^^
|Portal| 2025-06-30 14:22:07 [!!] KeyError: 'throttle'
|Portal| None|Portal| 2025-06-30 14:22:07 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 14:22:07 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 14:22:07 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
|Portal| 2025-06-30 14:22:07 [!!]     return getattr(self._connections, alias)
|Portal| 2025-06-30 14:22:07 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 14:22:07 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
|Portal| 2025-06-30 14:22:07 [!!]     return getattr(storage, key)
|Portal| 2025-06-30 14:22:07 [!!]            ^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 14:22:07 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
|Portal| 2025-06-30 14:22:07 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
|Portal| 2025-06-30 14:22:07 [!!] AttributeError: <asgiref.local._CVar object at 0x7b86a345f650> object has no attribute 'throttle'
|Portal| None|Portal| 2025-06-30 14:22:07 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 14:22:07 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 14:22:07 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
|Portal| 2025-06-30 14:22:07 [!!]     self.storage = caches["throttle"]
|Portal| 2025-06-30 14:22:07 [!!]                    ~~~~~~^^^^^^^^^^^^
|Portal| 2025-06-30 14:22:07 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
|Portal| 2025-06-30 14:22:07 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
|Portal| 2025-06-30 14:22:07 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
|Portal| 2025-06-30 14:22:07 [!!] Throttle: Errors encountered; using default cache.
|Portal| 2025-06-30 14:22:08 [..] Loaded.
|Portal| 2025-06-30 14:22:08 [..] twistd 23.10.0 (/usr/bin/python3.12 3.12.3) starting up.
|Portal| 2025-06-30 14:22:08 [..] reactor class: twisted.internet.epollreactor.EPollReactor.
|Portal| 2025-06-30 14:22:08 [..] AMP starting on 4006
|Portal| 2025-06-30 14:22:08 [..] Telnet starting on 4000
|Portal| 2025-06-30 14:22:08 [..] Websocket starting on 4002
|Portal| 2025-06-30 14:22:08 [..] Webserver-proxy starting on 4001
|Portal| 2025-06-30 14:22:08 [..] Portal starting server ... 
|Portal| 2025-06-30 14:22:12 [..] (TCP Port 4001 Closed)
|Portal| 2025-06-30 14:22:12 [..] (TCP Port 4002 Closed)
|Portal| 2025-06-30 14:22:12 [..] (TCP Port 4000 Closed)
|Portal| 2025-06-30 14:22:12 [..] (TCP Port 4006 Closed)
|Portal| 2025-06-30 14:22:12 [..] Main loop terminated.
|Portal| 2025-06-30 14:22:12 [..] Server Shut Down.
|Portal| 2025-06-30 14:23:49 [..] Loaded.
|Portal| 2025-06-30 14:23:48 [..] Loading /home/<USER>/.local/lib/python3.12/site-packages/evennia/server/portal/portal.py...
|Portal| 2025-06-30 14:23:48 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 14:23:48 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
|Portal| 2025-06-30 14:23:48 [!!]     return storage_object[key]
|Portal| 2025-06-30 14:23:48 [!!]            ~~~~~~~~~~~~~~^^^^^
|Portal| 2025-06-30 14:23:48 [!!] KeyError: 'throttle'
|Portal| None|Portal| 2025-06-30 14:23:48 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 14:23:48 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 14:23:48 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
|Portal| 2025-06-30 14:23:48 [!!]     return getattr(self._connections, alias)
|Portal| 2025-06-30 14:23:48 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 14:23:48 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
|Portal| 2025-06-30 14:23:48 [!!]     return getattr(storage, key)
|Portal| 2025-06-30 14:23:48 [!!]            ^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 14:23:48 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
|Portal| 2025-06-30 14:23:48 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
|Portal| 2025-06-30 14:23:48 [!!] AttributeError: <asgiref.local._CVar object at 0x725ba9a632c0> object has no attribute 'throttle'
|Portal| None|Portal| 2025-06-30 14:23:48 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 14:23:48 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 14:23:48 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
|Portal| 2025-06-30 14:23:48 [!!]     self.storage = caches["throttle"]
|Portal| 2025-06-30 14:23:48 [!!]                    ~~~~~~^^^^^^^^^^^^
|Portal| 2025-06-30 14:23:48 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
|Portal| 2025-06-30 14:23:48 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
|Portal| 2025-06-30 14:23:48 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
|Portal| 2025-06-30 14:23:48 [!!] Throttle: Errors encountered; using default cache.
|Portal| 2025-06-30 14:23:48 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 14:23:48 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
|Portal| 2025-06-30 14:23:48 [!!]     return storage_object[key]
|Portal| 2025-06-30 14:23:48 [!!]            ~~~~~~~~~~~~~~^^^^^
|Portal| 2025-06-30 14:23:48 [!!] KeyError: 'throttle'
|Portal| None|Portal| 2025-06-30 14:23:48 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 14:23:48 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 14:23:48 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
|Portal| 2025-06-30 14:23:48 [!!]     return getattr(self._connections, alias)
|Portal| 2025-06-30 14:23:48 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 14:23:48 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
|Portal| 2025-06-30 14:23:48 [!!]     return getattr(storage, key)
|Portal| 2025-06-30 14:23:48 [!!]            ^^^^^^^^^^^^^^^^^^^^^
|Portal| 2025-06-30 14:23:48 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
|Portal| 2025-06-30 14:23:48 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
|Portal| 2025-06-30 14:23:48 [!!] AttributeError: <asgiref.local._CVar object at 0x725ba9a632c0> object has no attribute 'throttle'
|Portal| None|Portal| 2025-06-30 14:23:48 [!!] During handling of the above exception, another exception occurred:
|Portal| None|Portal| 2025-06-30 14:23:48 [!!] Traceback (most recent call last):
|Portal| 2025-06-30 14:23:48 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
|Portal| 2025-06-30 14:23:48 [!!]     self.storage = caches["throttle"]
|Portal| 2025-06-30 14:23:48 [!!]                    ~~~~~~^^^^^^^^^^^^
|Portal| 2025-06-30 14:23:48 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
|Portal| 2025-06-30 14:23:48 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
|Portal| 2025-06-30 14:23:48 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
|Portal| 2025-06-30 14:23:48 [!!] Throttle: Errors encountered; using default cache.
|Portal| 2025-06-30 14:23:49 [..] Loaded.
|Portal| 2025-06-30 14:23:49 [..] twistd 23.10.0 (/usr/bin/python3.12 3.12.3) starting up.
|Portal| 2025-06-30 14:23:49 [..] reactor class: twisted.internet.epollreactor.EPollReactor.
|Portal| 2025-06-30 14:23:49 [..] AMP starting on 4006
|Portal| 2025-06-30 14:23:49 [..] Telnet starting on 4000
|Portal| 2025-06-30 14:23:49 [..] Websocket starting on 4002
|Portal| 2025-06-30 14:23:49 [..] Webserver-proxy starting on 4001
|Portal| 2025-06-30 14:23:49 [..] Portal starting server ... 
|Portal| 2025-06-30 14:23:55 [..] Portal starting server ... 
|Portal| 2025-06-30 14:24:51 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=32794)
|Portal| 2025-06-30 14:24:57 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=32786)
|Portal| 2025-06-30 14:25:01 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=32784)
|Portal| 2025-06-30 14:33:28 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=51074)
|Portal| 2025-06-30 14:33:28 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=51090)
|Portal| 2025-06-30 14:33:28 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=51102)
|Portal| 2025-06-30 14:33:57 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=43068)
|Portal| 2025-06-30 14:37:09 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=45940)
|Portal| 2025-06-30 14:37:09 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=45928)
|Portal| 2025-06-30 14:38:48 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=58798)
|Portal| 2025-06-30 14:38:48 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=58796)
|Portal| 2025-06-30 14:40:12 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=53376)
|Portal| 2025-06-30 14:40:12 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=53362)
|Portal| 2025-06-30 14:40:12 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=53348)
|Portal| 2025-06-30 14:40:12 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=50868)
|Portal| 2025-06-30 14:40:12 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=50876)
|Portal| 2025-06-30 14:40:12 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=53392)
|Portal| 2025-06-30 14:42:35 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=41092)
|Portal| 2025-06-30 14:42:35 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=41082)
|Portal| 2025-06-30 14:43:38 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=53670)
|Portal| 2025-06-30 14:43:38 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=53658)
|Portal| 2025-06-30 14:46:10 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=37064)
|Portal| 2025-06-30 14:46:26 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=37062)
|Portal| 2025-06-30 14:50:09 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=50292)
|Portal| 2025-06-30 14:50:09 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=50276)
|Portal| 2025-06-30 14:51:32 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=51368)
|Portal| 2025-06-30 14:51:32 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=51354)
|Portal| 2025-06-30 14:52:55 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=41958)
|Portal| 2025-06-30 14:53:30 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=41952)
|Portal| 2025-06-30 14:55:29 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=47302)
|Portal| 2025-06-30 14:55:30 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=47298)

<!DOCTYPE html>
<html>
<head>
    <title>修仙MUD Browser Tools测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f0f8ff; }
        h1 { color: #2c3e50; text-align: center; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .status { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .button { background: #3498db; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 10px; }
        .button:hover { background: #2980b9; }
        .log { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 修仙MUD Browser Tools 演示</h1>
        
        <div class="status">
            <h2>✅ 系统状态检查</h2>
            <ul>
                <li id="web-status">Web服务器: 运行中</li>
                <li id="tools-status">Browser Tools: 连接测试中...</li>
                <li id="evennia-status">Evennia服务器: 待检查</li>
            </ul>
        </div>

        <div>
            <h2>🛠️ Browser Tools 功能测试</h2>
            <button class="button" onclick="testConsole()">测试控制台日志</button>
            <button class="button" onclick="testAlert()">测试弹窗</button>
            <button class="button" onclick="testNetwork()">测试网络请求</button>
            <button class="button" onclick="checkEvennia()">检查Evennia状态</button>
        </div>

        <div id="log-container">
            <h3>📋 测试日志</h3>
            <div id="logs" class="log">准备开始Browser Tools测试...</div>
        </div>

        <div>
            <h2>🎯 修仙MUD功能演示</h2>
            <p>这是一个基于Evennia框架开发的修仙主题MUD游戏：</p>
            <ul>
                <li><strong>AI Director</strong>: 智能故事规划引擎</li>
                <li><strong>修仙系统</strong>: 炼气、筑基、金丹、元婴等境界</li>
                <li><strong>Handler生态</strong>: 模块化游戏逻辑处理</li>
                <li><strong>事件系统</strong>: 响应式游戏事件处理</li>
                <li><strong>Web API</strong>: RESTful接口支持</li>
            </ul>
        </div>
    </div>

    <script>
        console.log('🎮 修仙MUD Browser Tools演示页面加载完成');
        console.log('📊 当前时间:', new Date().toLocaleString());
        
        let logCount = 0;
        
        function addLog(message) {
            logCount++;
            const logs = document.getElementById('logs');
            logs.innerHTML += `<br>[${new Date().toLocaleTimeString()}] ${logCount}. ${message}`;
            console.log(`测试日志 ${logCount}: ${message}`);
        }
        
        function testConsole() {
            addLog('✅ 控制台日志测试 - 这条消息应该出现在Browser Tools中');
            console.warn('⚠️ 这是一个警告消息');
            console.error('❌ 这是一个错误消息（测试用）');
        }
        
        function testAlert() {
            addLog('🔔 弹窗测试开始');
            alert('🎉 Browser Tools弹窗测试成功！');
            addLog('✅ 弹窗测试完成');
        }
        
        function testNetwork() {
            addLog('🌐 网络请求测试开始');
            fetch('/test-api')
                .then(response => {
                    addLog(`📡 网络请求状态: ${response.status}`);
                })
                .catch(error => {
                    addLog(`❌ 网络请求失败: ${error.message}`);
                });
        }
        
        function checkEvennia() {
            addLog('🔍 检查Evennia服务器状态');
            fetch('http://localhost:4001')
                .then(response => {
                    addLog(`✅ Evennia服务器响应: ${response.status}`);
                    document.getElementById('evennia-status').innerHTML = 'Evennia服务器: ✅ 运行中';
                })
                .catch(error => {
                    addLog(`❌ Evennia服务器连接失败: ${error.message}`);
                    document.getElementById('evennia-status').innerHTML = 'Evennia服务器: ❌ 连接失败';
                });
        }
        
        // 页面加载完成后自动运行初始测试
        window.onload = function() {
            addLog('🚀 页面加载完成，开始初始化测试');
            document.getElementById('tools-status').innerHTML = 'Browser Tools: ✅ 页面加载成功';
            setTimeout(() => {
                addLog('⏰ 5秒后将自动检查Evennia状态');
                setTimeout(checkEvennia, 5000);
            }, 1000);
        };
        
        // 定期输出日志用于测试
        setInterval(() => {
            console.log(`📊 定期状态检查 - ${new Date().toLocaleTimeString()}`);
        }, 10000);
    </script>
</body>
</html>
#!/usr/bin/env python3
"""
创建Evennia超级用户的脚本
解决非TTY环境下无法创建超级用户的问题
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')

# 初始化Django
django.setup()

from evennia.accounts.models import AccountDB
from evennia.objects.models import ObjectDB
from evennia.typeclasses.models import TypeclassBase

def create_superuser():
    """创建超级用户账户"""
    
    print("开始创建Evennia超级用户...")
    
    try:
        # 检查是否已存在超级用户
        try:
            existing_user = AccountDB.objects.get(id=1)
            print(f"超级用户已存在: {existing_user.username}")
            return existing_user
        except AccountDB.DoesNotExist:
            pass
        
        # 创建超级用户
        superuser = AccountDB.objects.create_user(
            username="admin",
            email="<EMAIL>", 
            password="admin123"
        )
        
        # 设置为超级用户
        superuser.is_superuser = True
        superuser.is_staff = True
        superuser.save()
        
        print(f"✅ 成功创建超级用户: {superuser.username}")
        print(f"   用户ID: {superuser.id}")
        print(f"   邮箱: {superuser.email}")
        print(f"   超级用户权限: {superuser.is_superuser}")
        
        # 创建默认角色
        try:
            from typeclasses.characters import Character
            character = Character.objects.create(
                db_key="AdminChar",
                db_typeclass_path="typeclasses.characters.Character"
            )
            character.save()
            
            # 关联角色和账户
            superuser.db_characters.add(character)
            print(f"✅ 创建默认角色: {character.key}")
            
        except Exception as e:
            print(f"⚠️ 创建默认角色失败 (非关键): {e}")
        
        return superuser
        
    except Exception as e:
        print(f"❌ 创建超级用户失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def verify_database():
    """验证数据库状态"""
    
    print("\n验证数据库状态...")
    
    try:
        # 检查AccountDB表
        account_count = AccountDB.objects.count()
        print(f"账户数量: {account_count}")
        
        # 检查ObjectDB表  
        object_count = ObjectDB.objects.count()
        print(f"对象数量: {object_count}")
        
        # 检查超级用户
        try:
            superuser = AccountDB.objects.get(id=1)
            print(f"超级用户: {superuser.username} (ID: {superuser.id})")
        except AccountDB.DoesNotExist:
            print("⚠️ 未找到超级用户")
            
        print("✅ 数据库验证完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库验证失败: {e}")
        return False

if __name__ == "__main__":
    print("🎭 修仙MUD - 超级用户创建工具")
    print("=" * 50)
    
    # 验证数据库
    if not verify_database():
        sys.exit(1)
    
    # 创建超级用户
    superuser = create_superuser()
    
    if superuser:
        print("\n🎉 超级用户创建成功!")
        print("=" * 50)
        print("登录信息:")
        print(f"  用户名: {superuser.username}")
        print("  密码: admin123")
        print("  Web管理: http://localhost:4001/admin")
        print("  游戏客户端: telnet localhost 4000")
        print("=" * 50)
        sys.exit(0)
    else:
        print("\n❌ 超级用户创建失败!")
        sys.exit(1)
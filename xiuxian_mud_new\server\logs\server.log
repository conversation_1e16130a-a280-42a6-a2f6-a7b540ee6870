2025-06-30 13:16:03 [..] Loaded.
2025-06-30 13:16:01 [..] Loading /home/<USER>/.local/lib/python3.12/site-packages/evennia/server/server.py...
2025-06-30 13:16:01 [!!] Traceback (most recent call last):
2025-06-30 13:16:01 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
2025-06-30 13:16:01 [!!]     return storage_object[key]
2025-06-30 13:16:01 [!!]            ~~~~~~~~~~~~~~^^^^^
2025-06-30 13:16:01 [!!] KeyError: 'throttle'
None2025-06-30 13:16:01 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:16:01 [!!] Traceback (most recent call last):
2025-06-30 13:16:01 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
2025-06-30 13:16:01 [!!]     return getattr(self._connections, alias)
2025-06-30 13:16:01 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:16:01 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
2025-06-30 13:16:01 [!!]     return getattr(storage, key)
2025-06-30 13:16:01 [!!]            ^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:16:01 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
2025-06-30 13:16:01 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
2025-06-30 13:16:01 [!!] AttributeError: <asgiref.local._CVar object at 0x74a5f5af7860> object has no attribute 'throttle'
None2025-06-30 13:16:01 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:16:01 [!!] Traceback (most recent call last):
2025-06-30 13:16:01 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
2025-06-30 13:16:01 [!!]     self.storage = caches["throttle"]
2025-06-30 13:16:01 [!!]                    ~~~~~~^^^^^^^^^^^^
2025-06-30 13:16:01 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
2025-06-30 13:16:01 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
2025-06-30 13:16:01 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
2025-06-30 13:16:01 [!!] Throttle: Errors encountered; using default cache.
2025-06-30 13:16:01 [!!] Traceback (most recent call last):
2025-06-30 13:16:01 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
2025-06-30 13:16:01 [!!]     return storage_object[key]
2025-06-30 13:16:01 [!!]            ~~~~~~~~~~~~~~^^^^^
2025-06-30 13:16:01 [!!] KeyError: 'throttle'
None2025-06-30 13:16:01 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:16:01 [!!] Traceback (most recent call last):
2025-06-30 13:16:01 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
2025-06-30 13:16:01 [!!]     return getattr(self._connections, alias)
2025-06-30 13:16:01 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:16:01 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
2025-06-30 13:16:01 [!!]     return getattr(storage, key)
2025-06-30 13:16:01 [!!]            ^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:16:01 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
2025-06-30 13:16:01 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
2025-06-30 13:16:01 [!!] AttributeError: <asgiref.local._CVar object at 0x74a5f5af7860> object has no attribute 'throttle'
None2025-06-30 13:16:01 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:16:01 [!!] Traceback (most recent call last):
2025-06-30 13:16:01 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
2025-06-30 13:16:01 [!!]     self.storage = caches["throttle"]
2025-06-30 13:16:01 [!!]                    ~~~~~~^^^^^^^^^^^^
2025-06-30 13:16:01 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
2025-06-30 13:16:01 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
2025-06-30 13:16:01 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
2025-06-30 13:16:01 [!!] Throttle: Errors encountered; using default cache.
2025-06-30 13:16:03 [..] Loaded.
2025-06-30 13:16:03 [..] twistd 23.10.0 (/usr/bin/python3.12 3.12.3) starting up.
2025-06-30 13:16:03 [..] reactor class: twisted.internet.epollreactor.EPollReactor.
2025-06-30 13:16:03 [..] Webserver starting on 4005
2025-06-30 13:16:03 [..] Initial setup: Creating objects (Account #1 and Limbo room) ...
2025-06-30 13:16:03 [..] Handler registered: cultivationhandler in category 'cultivation'
2025-06-30 13:16:03 [..] Handler registered: combatskillhandler in category 'combat'
2025-06-30 13:16:03 [..] Handler registered: alchemyhandler in category 'alchemy'
2025-06-30 13:16:03 [..] Handler registered: karmahandler in category 'karma'
2025-06-30 13:16:03 [..] 使用全局AI配置
2025-06-30 13:16:03 [..] Handler registered: aidirectorhandler in category 'ai_director'
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] 事件总线初始化警告: Cannot force an update in save() with no primary key.
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:16:03 [..] Unable to format event {'prefix': '', 'line': '[Handler] admin.CultivationHandler: CultivationHandler initialized', 'log_logger': <Logger 'evennia.utils.logger'>, 'log_level': <LogLevel=info>, 'log_namespace': 'evennia.utils.logger', 'log_source': None, 'log_format': '{line}', 'log_time': 1751289363.9441068, 'log_system': '..'}: maximum recursion depth exceeded
2025-06-30 13:16:03 [EE] maximum recursion depth exceeded
2025-06-30 13:16:03 [..] Traceback (most recent call last):
2025-06-30 13:16:03 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 101, in create_objects
2025-06-30 13:16:03 [..]     superuser_character = ObjectDB.objects.get(id=1)
2025-06-30 13:16:03 [..]                           ^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:16:03 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/utils/idmapper/manager.py", line 33, in get
2025-06-30 13:16:03 [..]     inst = super().get(*args, **kwargs)
2025-06-30 13:16:03 [..]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:16:03 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/db/models/manager.py", line 87, in manager_method
2025-06-30 13:16:03 [..]     return getattr(self.get_queryset(), name)(*args, **kwargs)
2025-06-30 13:16:03 [..]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:16:03 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/db/models/query.py", line 637, in get
2025-06-30 13:16:03 [..]     raise self.model.DoesNotExist(
2025-06-30 13:16:03 [..] evennia.objects.models.ObjectDB.DoesNotExist: ObjectDB matching query does not exist.
None2025-06-30 13:16:03 [..] During handling of the above exception, another exception occurred:
None2025-06-30 13:16:03 [..] Traceback (most recent call last):
2025-06-30 13:16:03 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/service.py", line 395, in run_initial_setup
2025-06-30 13:16:03 [..]     initial_setup.handle_setup()
2025-06-30 13:16:03 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 217, in handle_setup
2025-06-30 13:16:03 [..]     setup_sequence[stepname]()
2025-06-30 13:16:03 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 107, in create_objects
2025-06-30 13:16:03 [..]     raise Exception(str(errors))
2025-06-30 13:16:03 [..] Exception: ["An error occurred while creating object 'admin object: maximum recursion depth exceeded"]
None2025-06-30 13:16:03 [..] Error in initial setup. Stopping Server + Portal.
2025-06-30 13:16:04 [..] Evennia Server successfully started.
2025-06-30 13:16:05 [..] Server disconnected from the portal.
2025-06-30 13:16:05 [..] Main loop terminated.
2025-06-30 13:16:05 [..] Server Shut Down.
2025-06-30 13:30:53 [..] Loaded.
2025-06-30 13:30:51 [..] Loading /home/<USER>/.local/lib/python3.12/site-packages/evennia/server/server.py...
2025-06-30 13:30:51 [!!] Traceback (most recent call last):
2025-06-30 13:30:51 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
2025-06-30 13:30:51 [!!]     return storage_object[key]
2025-06-30 13:30:51 [!!]            ~~~~~~~~~~~~~~^^^^^
2025-06-30 13:30:51 [!!] KeyError: 'throttle'
None2025-06-30 13:30:51 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:30:51 [!!] Traceback (most recent call last):
2025-06-30 13:30:51 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
2025-06-30 13:30:51 [!!]     return getattr(self._connections, alias)
2025-06-30 13:30:51 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:30:51 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
2025-06-30 13:30:51 [!!]     return getattr(storage, key)
2025-06-30 13:30:51 [!!]            ^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:30:51 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
2025-06-30 13:30:51 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
2025-06-30 13:30:51 [!!] AttributeError: <asgiref.local._CVar object at 0x75bfc39e7470> object has no attribute 'throttle'
None2025-06-30 13:30:51 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:30:51 [!!] Traceback (most recent call last):
2025-06-30 13:30:51 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
2025-06-30 13:30:51 [!!]     self.storage = caches["throttle"]
2025-06-30 13:30:51 [!!]                    ~~~~~~^^^^^^^^^^^^
2025-06-30 13:30:51 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
2025-06-30 13:30:51 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
2025-06-30 13:30:51 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
2025-06-30 13:30:51 [!!] Throttle: Errors encountered; using default cache.
2025-06-30 13:30:51 [!!] Traceback (most recent call last):
2025-06-30 13:30:51 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
2025-06-30 13:30:51 [!!]     return storage_object[key]
2025-06-30 13:30:51 [!!]            ~~~~~~~~~~~~~~^^^^^
2025-06-30 13:30:51 [!!] KeyError: 'throttle'
None2025-06-30 13:30:51 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:30:51 [!!] Traceback (most recent call last):
2025-06-30 13:30:51 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
2025-06-30 13:30:51 [!!]     return getattr(self._connections, alias)
2025-06-30 13:30:51 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:30:51 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
2025-06-30 13:30:51 [!!]     return getattr(storage, key)
2025-06-30 13:30:51 [!!]            ^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:30:51 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
2025-06-30 13:30:51 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
2025-06-30 13:30:51 [!!] AttributeError: <asgiref.local._CVar object at 0x75bfc39e7470> object has no attribute 'throttle'
None2025-06-30 13:30:51 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:30:51 [!!] Traceback (most recent call last):
2025-06-30 13:30:51 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
2025-06-30 13:30:51 [!!]     self.storage = caches["throttle"]
2025-06-30 13:30:51 [!!]                    ~~~~~~^^^^^^^^^^^^
2025-06-30 13:30:51 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
2025-06-30 13:30:51 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
2025-06-30 13:30:51 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
2025-06-30 13:30:51 [!!] Throttle: Errors encountered; using default cache.
2025-06-30 13:30:53 [..] Loaded.
2025-06-30 13:30:53 [..] twistd 23.10.0 (/usr/bin/python3.12 3.12.3) starting up.
2025-06-30 13:30:53 [..] reactor class: twisted.internet.epollreactor.EPollReactor.
2025-06-30 13:30:53 [..] Webserver starting on 4005
2025-06-30 13:30:53 [..] Initial setup: Creating objects (Account #1 and Limbo room) ...
2025-06-30 13:30:53 [..] Handler registered: cultivationhandler in category 'cultivation'
2025-06-30 13:30:53 [..] Handler registered: combatskillhandler in category 'combat'
2025-06-30 13:30:53 [..] Handler registered: alchemyhandler in category 'alchemy'
2025-06-30 13:30:53 [..] Handler registered: karmahandler in category 'karma'
2025-06-30 13:30:53 [..] 使用全局AI配置
2025-06-30 13:30:53 [..] Handler registered: aidirectorhandler in category 'ai_director'
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] 事件总线初始化警告: Cannot force an update in save() with no primary key.
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:30:54 [..] Unable to format event {'prefix': '', 'line': '[Handler] admin.CultivationHandler: CultivationHandler initialized', 'log_logger': <Logger 'evennia.utils.logger'>, 'log_level': <LogLevel=info>, 'log_namespace': 'evennia.utils.logger', 'log_source': None, 'log_format': '{line}', 'log_time': 1751290254.158002, 'log_system': '..'}: maximum recursion depth exceeded
2025-06-30 13:30:54 [EE] maximum recursion depth exceeded
2025-06-30 13:30:54 [..] Traceback (most recent call last):
2025-06-30 13:30:54 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 101, in create_objects
2025-06-30 13:30:54 [..]     superuser_character = ObjectDB.objects.get(id=1)
2025-06-30 13:30:54 [..]                           ^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:30:54 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/utils/idmapper/manager.py", line 33, in get
2025-06-30 13:30:54 [..]     inst = super().get(*args, **kwargs)
2025-06-30 13:30:54 [..]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:30:54 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/db/models/manager.py", line 87, in manager_method
2025-06-30 13:30:54 [..]     return getattr(self.get_queryset(), name)(*args, **kwargs)
2025-06-30 13:30:54 [..]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:30:54 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/db/models/query.py", line 637, in get
2025-06-30 13:30:54 [..]     raise self.model.DoesNotExist(
2025-06-30 13:30:54 [..] evennia.objects.models.ObjectDB.DoesNotExist: ObjectDB matching query does not exist.
None2025-06-30 13:30:54 [..] During handling of the above exception, another exception occurred:
None2025-06-30 13:30:54 [..] Traceback (most recent call last):
2025-06-30 13:30:54 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/service.py", line 395, in run_initial_setup
2025-06-30 13:30:54 [..]     initial_setup.handle_setup()
2025-06-30 13:30:54 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 217, in handle_setup
2025-06-30 13:30:54 [..]     setup_sequence[stepname]()
2025-06-30 13:30:54 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 107, in create_objects
2025-06-30 13:30:54 [..]     raise Exception(str(errors))
2025-06-30 13:30:54 [..] Exception: ["An error occurred while creating object 'admin object: maximum recursion depth exceeded"]
None2025-06-30 13:30:54 [..] Error in initial setup. Stopping Server + Portal.
2025-06-30 13:30:54 [..] Evennia Server successfully started.
2025-06-30 13:30:55 [..] Server disconnected from the portal.
2025-06-30 13:30:55 [..] Main loop terminated.
2025-06-30 13:30:55 [..] Server Shut Down.
2025-06-30 13:33:04 [..] Loaded.
2025-06-30 13:33:03 [..] Loading /home/<USER>/.local/lib/python3.12/site-packages/evennia/server/server.py...
2025-06-30 13:33:03 [!!] Traceback (most recent call last):
2025-06-30 13:33:03 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
2025-06-30 13:33:03 [!!]     return storage_object[key]
2025-06-30 13:33:03 [!!]            ~~~~~~~~~~~~~~^^^^^
2025-06-30 13:33:03 [!!] KeyError: 'throttle'
None2025-06-30 13:33:03 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:33:03 [!!] Traceback (most recent call last):
2025-06-30 13:33:03 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
2025-06-30 13:33:03 [!!]     return getattr(self._connections, alias)
2025-06-30 13:33:03 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:33:03 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
2025-06-30 13:33:03 [!!]     return getattr(storage, key)
2025-06-30 13:33:03 [!!]            ^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:33:03 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
2025-06-30 13:33:03 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
2025-06-30 13:33:03 [!!] AttributeError: <asgiref.local._CVar object at 0x76c992a83b00> object has no attribute 'throttle'
None2025-06-30 13:33:03 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:33:03 [!!] Traceback (most recent call last):
2025-06-30 13:33:03 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
2025-06-30 13:33:03 [!!]     self.storage = caches["throttle"]
2025-06-30 13:33:03 [!!]                    ~~~~~~^^^^^^^^^^^^
2025-06-30 13:33:03 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
2025-06-30 13:33:03 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
2025-06-30 13:33:03 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
2025-06-30 13:33:03 [!!] Throttle: Errors encountered; using default cache.
2025-06-30 13:33:03 [!!] Traceback (most recent call last):
2025-06-30 13:33:03 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
2025-06-30 13:33:03 [!!]     return storage_object[key]
2025-06-30 13:33:03 [!!]            ~~~~~~~~~~~~~~^^^^^
2025-06-30 13:33:03 [!!] KeyError: 'throttle'
None2025-06-30 13:33:03 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:33:03 [!!] Traceback (most recent call last):
2025-06-30 13:33:03 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
2025-06-30 13:33:03 [!!]     return getattr(self._connections, alias)
2025-06-30 13:33:03 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:33:03 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
2025-06-30 13:33:03 [!!]     return getattr(storage, key)
2025-06-30 13:33:03 [!!]            ^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:33:03 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
2025-06-30 13:33:03 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
2025-06-30 13:33:03 [!!] AttributeError: <asgiref.local._CVar object at 0x76c992a83b00> object has no attribute 'throttle'
None2025-06-30 13:33:03 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:33:03 [!!] Traceback (most recent call last):
2025-06-30 13:33:03 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
2025-06-30 13:33:03 [!!]     self.storage = caches["throttle"]
2025-06-30 13:33:03 [!!]                    ~~~~~~^^^^^^^^^^^^
2025-06-30 13:33:03 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
2025-06-30 13:33:03 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
2025-06-30 13:33:03 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
2025-06-30 13:33:03 [!!] Throttle: Errors encountered; using default cache.
2025-06-30 13:33:04 [..] Loaded.
2025-06-30 13:33:04 [..] twistd 23.10.0 (/usr/bin/python3.12 3.12.3) starting up.
2025-06-30 13:33:04 [..] reactor class: twisted.internet.epollreactor.EPollReactor.
2025-06-30 13:33:04 [..] Webserver starting on 4005
2025-06-30 13:33:05 [..] Initial setup: Creating objects (Account #1 and Limbo room) ...
2025-06-30 13:33:05 [..] Handler registered: cultivationhandler in category 'cultivation'
2025-06-30 13:33:05 [..] Handler registered: combatskillhandler in category 'combat'
2025-06-30 13:33:05 [..] Handler registered: alchemyhandler in category 'alchemy'
2025-06-30 13:33:05 [..] Handler registered: karmahandler in category 'karma'
2025-06-30 13:33:05 [..] 使用全局AI配置
2025-06-30 13:33:05 [..] Handler registered: aidirectorhandler in category 'ai_director'
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] 事件总线初始化警告: Cannot force an update in save() with no primary key.
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:33:05 [..] Unable to format event {'prefix': '', 'line': '[Handler] admin.CultivationHandler: CultivationHandler initialized', 'log_logger': <Logger 'evennia.utils.logger'>, 'log_level': <LogLevel=info>, 'log_namespace': 'evennia.utils.logger', 'log_source': None, 'log_format': '{line}', 'log_time': 1751290385.8217137, 'log_system': '..'}: maximum recursion depth exceeded
2025-06-30 13:33:05 [EE] maximum recursion depth exceeded
2025-06-30 13:33:05 [..] Traceback (most recent call last):
2025-06-30 13:33:05 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 101, in create_objects
2025-06-30 13:33:05 [..]     superuser_character = ObjectDB.objects.get(id=1)
2025-06-30 13:33:05 [..]                           ^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:33:05 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/utils/idmapper/manager.py", line 33, in get
2025-06-30 13:33:05 [..]     inst = super().get(*args, **kwargs)
2025-06-30 13:33:05 [..]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:33:05 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/db/models/manager.py", line 87, in manager_method
2025-06-30 13:33:05 [..]     return getattr(self.get_queryset(), name)(*args, **kwargs)
2025-06-30 13:33:05 [..]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:33:05 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/db/models/query.py", line 637, in get
2025-06-30 13:33:05 [..]     raise self.model.DoesNotExist(
2025-06-30 13:33:05 [..] evennia.objects.models.ObjectDB.DoesNotExist: ObjectDB matching query does not exist.
None2025-06-30 13:33:05 [..] During handling of the above exception, another exception occurred:
None2025-06-30 13:33:05 [..] Traceback (most recent call last):
2025-06-30 13:33:05 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/service.py", line 395, in run_initial_setup
2025-06-30 13:33:05 [..]     initial_setup.handle_setup()
2025-06-30 13:33:05 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 217, in handle_setup
2025-06-30 13:33:05 [..]     setup_sequence[stepname]()
2025-06-30 13:33:05 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 107, in create_objects
2025-06-30 13:33:05 [..]     raise Exception(str(errors))
2025-06-30 13:33:05 [..] Exception: ["An error occurred while creating object 'admin object: maximum recursion depth exceeded"]
None2025-06-30 13:33:05 [..] Error in initial setup. Stopping Server + Portal.
2025-06-30 13:33:05 [..] Evennia Server successfully started.
2025-06-30 13:33:06 [..] Server disconnected from the portal.
2025-06-30 13:33:06 [..] Main loop terminated.
2025-06-30 13:33:06 [..] Server Shut Down.
2025-06-30 13:35:42 [..] Loaded.
2025-06-30 13:35:40 [..] Loading /home/<USER>/.local/lib/python3.12/site-packages/evennia/server/server.py...
2025-06-30 13:35:40 [!!] Traceback (most recent call last):
2025-06-30 13:35:40 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
2025-06-30 13:35:40 [!!]     return storage_object[key]
2025-06-30 13:35:40 [!!]            ~~~~~~~~~~~~~~^^^^^
2025-06-30 13:35:40 [!!] KeyError: 'throttle'
None2025-06-30 13:35:40 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:35:40 [!!] Traceback (most recent call last):
2025-06-30 13:35:40 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
2025-06-30 13:35:40 [!!]     return getattr(self._connections, alias)
2025-06-30 13:35:40 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:35:40 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
2025-06-30 13:35:40 [!!]     return getattr(storage, key)
2025-06-30 13:35:40 [!!]            ^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:35:40 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
2025-06-30 13:35:40 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
2025-06-30 13:35:40 [!!] AttributeError: <asgiref.local._CVar object at 0x770bb46f3980> object has no attribute 'throttle'
None2025-06-30 13:35:40 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:35:40 [!!] Traceback (most recent call last):
2025-06-30 13:35:40 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
2025-06-30 13:35:40 [!!]     self.storage = caches["throttle"]
2025-06-30 13:35:40 [!!]                    ~~~~~~^^^^^^^^^^^^
2025-06-30 13:35:40 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
2025-06-30 13:35:40 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
2025-06-30 13:35:40 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
2025-06-30 13:35:40 [!!] Throttle: Errors encountered; using default cache.
2025-06-30 13:35:40 [!!] Traceback (most recent call last):
2025-06-30 13:35:40 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
2025-06-30 13:35:40 [!!]     return storage_object[key]
2025-06-30 13:35:40 [!!]            ~~~~~~~~~~~~~~^^^^^
2025-06-30 13:35:40 [!!] KeyError: 'throttle'
None2025-06-30 13:35:40 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:35:40 [!!] Traceback (most recent call last):
2025-06-30 13:35:40 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
2025-06-30 13:35:40 [!!]     return getattr(self._connections, alias)
2025-06-30 13:35:40 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:35:40 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
2025-06-30 13:35:40 [!!]     return getattr(storage, key)
2025-06-30 13:35:40 [!!]            ^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:35:40 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
2025-06-30 13:35:40 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
2025-06-30 13:35:40 [!!] AttributeError: <asgiref.local._CVar object at 0x770bb46f3980> object has no attribute 'throttle'
None2025-06-30 13:35:40 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:35:40 [!!] Traceback (most recent call last):
2025-06-30 13:35:40 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
2025-06-30 13:35:40 [!!]     self.storage = caches["throttle"]
2025-06-30 13:35:40 [!!]                    ~~~~~~^^^^^^^^^^^^
2025-06-30 13:35:40 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
2025-06-30 13:35:40 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
2025-06-30 13:35:40 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
2025-06-30 13:35:40 [!!] Throttle: Errors encountered; using default cache.
2025-06-30 13:35:42 [..] Loaded.
2025-06-30 13:35:42 [..] twistd 23.10.0 (/usr/bin/python3.12 3.12.3) starting up.
2025-06-30 13:35:42 [..] reactor class: twisted.internet.epollreactor.EPollReactor.
2025-06-30 13:35:42 [..] Webserver starting on 4005
2025-06-30 13:35:42 [..] Initial setup: Creating objects (Account #1 and Limbo room) ...
2025-06-30 13:35:42 [..] Handler registered: cultivationhandler in category 'cultivation'
2025-06-30 13:35:42 [..] Handler registered: combatskillhandler in category 'combat'
2025-06-30 13:35:42 [..] Handler registered: alchemyhandler in category 'alchemy'
2025-06-30 13:35:42 [..] Handler registered: karmahandler in category 'karma'
2025-06-30 13:35:42 [..] 使用全局AI配置
2025-06-30 13:35:43 [..] Handler registered: aidirectorhandler in category 'ai_director'
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] 事件总线初始化警告: Cannot force an update in save() with no primary key.
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:35:43 [..] Unable to format event {'prefix': '', 'line': '[Handler] admin.CultivationHandler: CultivationHandler initialized', 'log_logger': <Logger 'evennia.utils.logger'>, 'log_level': <LogLevel=info>, 'log_namespace': 'evennia.utils.logger', 'log_source': None, 'log_format': '{line}', 'log_time': 1751290543.2076037, 'log_system': '..'}: maximum recursion depth exceeded
2025-06-30 13:35:43 [EE] maximum recursion depth exceeded
2025-06-30 13:35:43 [..] Traceback (most recent call last):
2025-06-30 13:35:43 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 101, in create_objects
2025-06-30 13:35:43 [..]     superuser_character = ObjectDB.objects.get(id=1)
2025-06-30 13:35:43 [..]                           ^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:35:43 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/utils/idmapper/manager.py", line 33, in get
2025-06-30 13:35:43 [..]     inst = super().get(*args, **kwargs)
2025-06-30 13:35:43 [..]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:35:43 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/db/models/manager.py", line 87, in manager_method
2025-06-30 13:35:43 [..]     return getattr(self.get_queryset(), name)(*args, **kwargs)
2025-06-30 13:35:43 [..]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:35:43 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/db/models/query.py", line 637, in get
2025-06-30 13:35:43 [..]     raise self.model.DoesNotExist(
2025-06-30 13:35:43 [..] evennia.objects.models.ObjectDB.DoesNotExist: ObjectDB matching query does not exist.
None2025-06-30 13:35:43 [..] During handling of the above exception, another exception occurred:
None2025-06-30 13:35:43 [..] Traceback (most recent call last):
2025-06-30 13:35:43 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/service.py", line 395, in run_initial_setup
2025-06-30 13:35:43 [..]     initial_setup.handle_setup()
2025-06-30 13:35:43 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 217, in handle_setup
2025-06-30 13:35:43 [..]     setup_sequence[stepname]()
2025-06-30 13:35:43 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 107, in create_objects
2025-06-30 13:35:43 [..]     raise Exception(str(errors))
2025-06-30 13:35:43 [..] Exception: ["An error occurred while creating object 'admin object: maximum recursion depth exceeded"]
None2025-06-30 13:35:43 [..] Error in initial setup. Stopping Server + Portal.
2025-06-30 13:35:43 [..] Evennia Server successfully started.
2025-06-30 13:35:44 [..] Server disconnected from the portal.
2025-06-30 13:35:44 [..] Main loop terminated.
2025-06-30 13:35:44 [..] Server Shut Down.
2025-06-30 13:41:00 [..] Loaded.
2025-06-30 13:40:58 [..] Loading /home/<USER>/.local/lib/python3.12/site-packages/evennia/server/server.py...
2025-06-30 13:40:59 [!!] Traceback (most recent call last):
2025-06-30 13:40:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
2025-06-30 13:40:59 [!!]     return storage_object[key]
2025-06-30 13:40:59 [!!]            ~~~~~~~~~~~~~~^^^^^
2025-06-30 13:40:59 [!!] KeyError: 'throttle'
None2025-06-30 13:40:59 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:40:59 [!!] Traceback (most recent call last):
2025-06-30 13:40:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
2025-06-30 13:40:59 [!!]     return getattr(self._connections, alias)
2025-06-30 13:40:59 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:40:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
2025-06-30 13:40:59 [!!]     return getattr(storage, key)
2025-06-30 13:40:59 [!!]            ^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:40:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
2025-06-30 13:40:59 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
2025-06-30 13:40:59 [!!] AttributeError: <asgiref.local._CVar object at 0x7d4b5088b260> object has no attribute 'throttle'
None2025-06-30 13:40:59 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:40:59 [!!] Traceback (most recent call last):
2025-06-30 13:40:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
2025-06-30 13:40:59 [!!]     self.storage = caches["throttle"]
2025-06-30 13:40:59 [!!]                    ~~~~~~^^^^^^^^^^^^
2025-06-30 13:40:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
2025-06-30 13:40:59 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
2025-06-30 13:40:59 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
2025-06-30 13:40:59 [!!] Throttle: Errors encountered; using default cache.
2025-06-30 13:40:59 [!!] Traceback (most recent call last):
2025-06-30 13:40:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
2025-06-30 13:40:59 [!!]     return storage_object[key]
2025-06-30 13:40:59 [!!]            ~~~~~~~~~~~~~~^^^^^
2025-06-30 13:40:59 [!!] KeyError: 'throttle'
None2025-06-30 13:40:59 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:40:59 [!!] Traceback (most recent call last):
2025-06-30 13:40:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
2025-06-30 13:40:59 [!!]     return getattr(self._connections, alias)
2025-06-30 13:40:59 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:40:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
2025-06-30 13:40:59 [!!]     return getattr(storage, key)
2025-06-30 13:40:59 [!!]            ^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:40:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
2025-06-30 13:40:59 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
2025-06-30 13:40:59 [!!] AttributeError: <asgiref.local._CVar object at 0x7d4b5088b260> object has no attribute 'throttle'
None2025-06-30 13:40:59 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:40:59 [!!] Traceback (most recent call last):
2025-06-30 13:40:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
2025-06-30 13:40:59 [!!]     self.storage = caches["throttle"]
2025-06-30 13:40:59 [!!]                    ~~~~~~^^^^^^^^^^^^
2025-06-30 13:40:59 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
2025-06-30 13:40:59 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
2025-06-30 13:40:59 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
2025-06-30 13:40:59 [!!] Throttle: Errors encountered; using default cache.
2025-06-30 13:41:00 [..] Loaded.
2025-06-30 13:41:00 [..] twistd 23.10.0 (/usr/bin/python3.12 3.12.3) starting up.
2025-06-30 13:41:00 [..] reactor class: twisted.internet.epollreactor.EPollReactor.
2025-06-30 13:41:00 [..] Webserver starting on 4005
2025-06-30 13:41:00 [..] Initial setup: Creating objects (Account #1 and Limbo room) ...
2025-06-30 13:41:00 [..] Handler registered: cultivationhandler in category 'cultivation'
2025-06-30 13:41:00 [..] Handler registered: combatskillhandler in category 'combat'
2025-06-30 13:41:00 [..] Handler registered: alchemyhandler in category 'alchemy'
2025-06-30 13:41:00 [..] Handler registered: karmahandler in category 'karma'
2025-06-30 13:41:00 [..] 使用全局AI配置
2025-06-30 13:41:01 [..] Handler registered: aidirectorhandler in category 'ai_director'
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] 事件总线初始化警告: Cannot force an update in save() with no primary key.
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:41:01 [..] Unable to format event {'prefix': '', 'line': '[Handler] admin.CultivationHandler: CultivationHandler initialized', 'log_logger': <Logger 'evennia.utils.logger'>, 'log_level': <LogLevel=info>, 'log_namespace': 'evennia.utils.logger', 'log_source': None, 'log_format': '{line}', 'log_time': 1751290861.4358723, 'log_system': '..'}: maximum recursion depth exceeded
2025-06-30 13:41:01 [EE] maximum recursion depth exceeded
2025-06-30 13:41:01 [..] Traceback (most recent call last):
2025-06-30 13:41:01 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 101, in create_objects
2025-06-30 13:41:01 [..]     superuser_character = ObjectDB.objects.get(id=1)
2025-06-30 13:41:01 [..]                           ^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:41:01 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/utils/idmapper/manager.py", line 33, in get
2025-06-30 13:41:01 [..]     inst = super().get(*args, **kwargs)
2025-06-30 13:41:01 [..]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:41:01 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/db/models/manager.py", line 87, in manager_method
2025-06-30 13:41:01 [..]     return getattr(self.get_queryset(), name)(*args, **kwargs)
2025-06-30 13:41:01 [..]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:41:01 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/db/models/query.py", line 637, in get
2025-06-30 13:41:01 [..]     raise self.model.DoesNotExist(
2025-06-30 13:41:01 [..] evennia.objects.models.ObjectDB.DoesNotExist: ObjectDB matching query does not exist.
None2025-06-30 13:41:01 [..] During handling of the above exception, another exception occurred:
None2025-06-30 13:41:01 [..] Traceback (most recent call last):
2025-06-30 13:41:01 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/service.py", line 395, in run_initial_setup
2025-06-30 13:41:01 [..]     initial_setup.handle_setup()
2025-06-30 13:41:01 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 217, in handle_setup
2025-06-30 13:41:01 [..]     setup_sequence[stepname]()
2025-06-30 13:41:01 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 107, in create_objects
2025-06-30 13:41:01 [..]     raise Exception(str(errors))
2025-06-30 13:41:01 [..] Exception: ["An error occurred while creating object 'admin object: maximum recursion depth exceeded"]
None2025-06-30 13:41:01 [..] Error in initial setup. Stopping Server + Portal.
2025-06-30 13:41:01 [..] Evennia Server successfully started.
2025-06-30 13:41:02 [..] Server disconnected from the portal.
2025-06-30 13:41:02 [..] Main loop terminated.
2025-06-30 13:41:02 [..] Server Shut Down.
2025-06-30 13:57:06 [..] Loaded.
2025-06-30 13:57:04 [..] Loading /home/<USER>/.local/lib/python3.12/site-packages/evennia/server/server.py...
2025-06-30 13:57:04 [!!] Traceback (most recent call last):
2025-06-30 13:57:04 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
2025-06-30 13:57:04 [!!]     return storage_object[key]
2025-06-30 13:57:04 [!!]            ~~~~~~~~~~~~~~^^^^^
2025-06-30 13:57:04 [!!] KeyError: 'throttle'
None2025-06-30 13:57:04 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:57:04 [!!] Traceback (most recent call last):
2025-06-30 13:57:04 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
2025-06-30 13:57:04 [!!]     return getattr(self._connections, alias)
2025-06-30 13:57:04 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:57:04 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
2025-06-30 13:57:04 [!!]     return getattr(storage, key)
2025-06-30 13:57:04 [!!]            ^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:57:04 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
2025-06-30 13:57:04 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
2025-06-30 13:57:04 [!!] AttributeError: <asgiref.local._CVar object at 0x764430f0b3b0> object has no attribute 'throttle'
None2025-06-30 13:57:04 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:57:04 [!!] Traceback (most recent call last):
2025-06-30 13:57:04 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
2025-06-30 13:57:04 [!!]     self.storage = caches["throttle"]
2025-06-30 13:57:04 [!!]                    ~~~~~~^^^^^^^^^^^^
2025-06-30 13:57:04 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
2025-06-30 13:57:04 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
2025-06-30 13:57:04 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
2025-06-30 13:57:04 [!!] Throttle: Errors encountered; using default cache.
2025-06-30 13:57:04 [!!] Traceback (most recent call last):
2025-06-30 13:57:04 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
2025-06-30 13:57:04 [!!]     return storage_object[key]
2025-06-30 13:57:04 [!!]            ~~~~~~~~~~~~~~^^^^^
2025-06-30 13:57:04 [!!] KeyError: 'throttle'
None2025-06-30 13:57:04 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:57:04 [!!] Traceback (most recent call last):
2025-06-30 13:57:04 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
2025-06-30 13:57:04 [!!]     return getattr(self._connections, alias)
2025-06-30 13:57:04 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:57:04 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
2025-06-30 13:57:04 [!!]     return getattr(storage, key)
2025-06-30 13:57:04 [!!]            ^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:57:04 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
2025-06-30 13:57:04 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
2025-06-30 13:57:04 [!!] AttributeError: <asgiref.local._CVar object at 0x764430f0b3b0> object has no attribute 'throttle'
None2025-06-30 13:57:04 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 13:57:04 [!!] Traceback (most recent call last):
2025-06-30 13:57:04 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
2025-06-30 13:57:04 [!!]     self.storage = caches["throttle"]
2025-06-30 13:57:04 [!!]                    ~~~~~~^^^^^^^^^^^^
2025-06-30 13:57:04 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
2025-06-30 13:57:04 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
2025-06-30 13:57:04 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
2025-06-30 13:57:04 [!!] Throttle: Errors encountered; using default cache.
2025-06-30 13:57:06 [..] Loaded.
2025-06-30 13:57:06 [..] twistd 23.10.0 (/usr/bin/python3.12 3.12.3) starting up.
2025-06-30 13:57:06 [..] reactor class: twisted.internet.epollreactor.EPollReactor.
2025-06-30 13:57:06 [..] Webserver starting on 4005
2025-06-30 13:57:06 [..] Initial setup: Creating objects (Account #1 and Limbo room) ...
2025-06-30 13:57:06 [..] Handler registered: cultivationhandler in category 'cultivation'
2025-06-30 13:57:06 [..] Handler registered: combatskillhandler in category 'combat'
2025-06-30 13:57:06 [..] Handler registered: alchemyhandler in category 'alchemy'
2025-06-30 13:57:06 [..] Handler registered: karmahandler in category 'karma'
2025-06-30 13:57:06 [..] 使用全局AI配置
2025-06-30 13:57:06 [..] Handler registered: aidirectorhandler in category 'ai_director'
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] 事件总线初始化警告: Cannot force an update in save() with no primary key.
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] [Handler] admin.CultivationHandler: CultivationHandler initialized
2025-06-30 13:57:07 [..] Unable to format event {'prefix': '', 'line': '[Handler] admin.CultivationHandler: CultivationHandler initialized', 'log_logger': <Logger 'evennia.utils.logger'>, 'log_level': <LogLevel=info>, 'log_namespace': 'evennia.utils.logger', 'log_source': None, 'log_format': '{line}', 'log_time': 1751291827.076995, 'log_system': '..'}: maximum recursion depth exceeded
2025-06-30 13:57:07 [EE] maximum recursion depth exceeded
2025-06-30 13:57:07 [..] Traceback (most recent call last):
2025-06-30 13:57:07 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 101, in create_objects
2025-06-30 13:57:07 [..]     superuser_character = ObjectDB.objects.get(id=1)
2025-06-30 13:57:07 [..]                           ^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:57:07 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/utils/idmapper/manager.py", line 33, in get
2025-06-30 13:57:07 [..]     inst = super().get(*args, **kwargs)
2025-06-30 13:57:07 [..]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:57:07 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/db/models/manager.py", line 87, in manager_method
2025-06-30 13:57:07 [..]     return getattr(self.get_queryset(), name)(*args, **kwargs)
2025-06-30 13:57:07 [..]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 13:57:07 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/db/models/query.py", line 637, in get
2025-06-30 13:57:07 [..]     raise self.model.DoesNotExist(
2025-06-30 13:57:07 [..] evennia.objects.models.ObjectDB.DoesNotExist: ObjectDB matching query does not exist.
None2025-06-30 13:57:07 [..] During handling of the above exception, another exception occurred:
None2025-06-30 13:57:07 [..] Traceback (most recent call last):
2025-06-30 13:57:07 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/service.py", line 395, in run_initial_setup
2025-06-30 13:57:07 [..]     initial_setup.handle_setup()
2025-06-30 13:57:07 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 217, in handle_setup
2025-06-30 13:57:07 [..]     setup_sequence[stepname]()
2025-06-30 13:57:07 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 107, in create_objects
2025-06-30 13:57:07 [..]     raise Exception(str(errors))
2025-06-30 13:57:07 [..] Exception: ["An error occurred while creating object 'admin object: maximum recursion depth exceeded"]
None2025-06-30 13:57:07 [..] Error in initial setup. Stopping Server + Portal.
2025-06-30 13:57:07 [..] Evennia Server successfully started.
2025-06-30 13:57:08 [..] Server disconnected from the portal.
2025-06-30 13:57:08 [..] Main loop terminated.
2025-06-30 13:57:08 [..] Server Shut Down.
2025-06-30 14:22:10 [..] Loaded.
2025-06-30 14:22:09 [..] Loading /home/<USER>/.local/lib/python3.12/site-packages/evennia/server/server.py...
2025-06-30 14:22:09 [!!] Traceback (most recent call last):
2025-06-30 14:22:09 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
2025-06-30 14:22:09 [!!]     return storage_object[key]
2025-06-30 14:22:09 [!!]            ~~~~~~~~~~~~~~^^^^^
2025-06-30 14:22:09 [!!] KeyError: 'throttle'
None2025-06-30 14:22:09 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 14:22:09 [!!] Traceback (most recent call last):
2025-06-30 14:22:09 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
2025-06-30 14:22:09 [!!]     return getattr(self._connections, alias)
2025-06-30 14:22:09 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 14:22:09 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
2025-06-30 14:22:09 [!!]     return getattr(storage, key)
2025-06-30 14:22:09 [!!]            ^^^^^^^^^^^^^^^^^^^^^
2025-06-30 14:22:09 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
2025-06-30 14:22:09 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
2025-06-30 14:22:09 [!!] AttributeError: <asgiref.local._CVar object at 0x7a011c8f74a0> object has no attribute 'throttle'
None2025-06-30 14:22:09 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 14:22:09 [!!] Traceback (most recent call last):
2025-06-30 14:22:09 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
2025-06-30 14:22:09 [!!]     self.storage = caches["throttle"]
2025-06-30 14:22:09 [!!]                    ~~~~~~^^^^^^^^^^^^
2025-06-30 14:22:09 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
2025-06-30 14:22:09 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
2025-06-30 14:22:09 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
2025-06-30 14:22:09 [!!] Throttle: Errors encountered; using default cache.
2025-06-30 14:22:09 [!!] Traceback (most recent call last):
2025-06-30 14:22:09 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
2025-06-30 14:22:09 [!!]     return storage_object[key]
2025-06-30 14:22:09 [!!]            ~~~~~~~~~~~~~~^^^^^
2025-06-30 14:22:09 [!!] KeyError: 'throttle'
None2025-06-30 14:22:09 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 14:22:09 [!!] Traceback (most recent call last):
2025-06-30 14:22:09 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
2025-06-30 14:22:09 [!!]     return getattr(self._connections, alias)
2025-06-30 14:22:09 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 14:22:09 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
2025-06-30 14:22:09 [!!]     return getattr(storage, key)
2025-06-30 14:22:09 [!!]            ^^^^^^^^^^^^^^^^^^^^^
2025-06-30 14:22:09 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
2025-06-30 14:22:09 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
2025-06-30 14:22:09 [!!] AttributeError: <asgiref.local._CVar object at 0x7a011c8f74a0> object has no attribute 'throttle'
None2025-06-30 14:22:09 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 14:22:09 [!!] Traceback (most recent call last):
2025-06-30 14:22:09 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
2025-06-30 14:22:09 [!!]     self.storage = caches["throttle"]
2025-06-30 14:22:09 [!!]                    ~~~~~~^^^^^^^^^^^^
2025-06-30 14:22:09 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
2025-06-30 14:22:09 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
2025-06-30 14:22:09 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
2025-06-30 14:22:09 [!!] Throttle: Errors encountered; using default cache.
2025-06-30 14:22:10 [..] Loaded.
2025-06-30 14:22:10 [..] twistd 23.10.0 (/usr/bin/python3.12 3.12.3) starting up.
2025-06-30 14:22:10 [..] reactor class: twisted.internet.epollreactor.EPollReactor.
2025-06-30 14:22:10 [..] Webserver starting on 4005
2025-06-30 14:22:10 [..] Initial setup: Creating objects (Account #1 and Limbo room) ...
2025-06-30 14:22:10 [..] Handler registered: cultivationhandler in category 'cultivation'
2025-06-30 14:22:10 [..] Handler registered: combatskillhandler in category 'combat'
2025-06-30 14:22:10 [..] Handler registered: alchemyhandler in category 'alchemy'
2025-06-30 14:22:10 [..] Handler registered: karmahandler in category 'karma'
2025-06-30 14:22:10 [..] 使用全局AI配置
2025-06-30 14:22:11 [..] Handler registered: aidirectorhandler in category 'ai_director'
2025-06-30 14:22:11 [EE] 'TagHandler' object has no attribute 'set'
2025-06-30 14:22:11 [..] Traceback (most recent call last):
2025-06-30 14:22:11 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 101, in create_objects
2025-06-30 14:22:11 [..]     superuser_character = ObjectDB.objects.get(id=1)
2025-06-30 14:22:11 [..]                           ^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 14:22:11 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/utils/idmapper/manager.py", line 33, in get
2025-06-30 14:22:11 [..]     inst = super().get(*args, **kwargs)
2025-06-30 14:22:11 [..]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 14:22:11 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/db/models/manager.py", line 87, in manager_method
2025-06-30 14:22:11 [..]     return getattr(self.get_queryset(), name)(*args, **kwargs)
2025-06-30 14:22:11 [..]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 14:22:11 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/db/models/query.py", line 637, in get
2025-06-30 14:22:11 [..]     raise self.model.DoesNotExist(
2025-06-30 14:22:11 [..] evennia.objects.models.ObjectDB.DoesNotExist: ObjectDB matching query does not exist.
None2025-06-30 14:22:11 [..] During handling of the above exception, another exception occurred:
None2025-06-30 14:22:11 [..] Traceback (most recent call last):
2025-06-30 14:22:11 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/service.py", line 395, in run_initial_setup
2025-06-30 14:22:11 [..]     initial_setup.handle_setup()
2025-06-30 14:22:11 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 217, in handle_setup
2025-06-30 14:22:11 [..]     setup_sequence[stepname]()
2025-06-30 14:22:11 [..]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/initial_setup.py", line 107, in create_objects
2025-06-30 14:22:11 [..]     raise Exception(str(errors))
2025-06-30 14:22:11 [..] Exception: ["An error occurred while creating object 'admin object: 'TagHandler' object has no attribute 'set'"]
None2025-06-30 14:22:11 [..] Error in initial setup. Stopping Server + Portal.
2025-06-30 14:22:11 [..] Evennia Server successfully started.
2025-06-30 14:22:12 [..] Server disconnected from the portal.
2025-06-30 14:22:12 [..] Main loop terminated.
2025-06-30 14:22:12 [..] Server Shut Down.
2025-06-30 14:23:51 [..] Loaded.
2025-06-30 14:23:50 [..] Loading /home/<USER>/.local/lib/python3.12/site-packages/evennia/server/server.py...
2025-06-30 14:23:50 [!!] Traceback (most recent call last):
2025-06-30 14:23:50 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
2025-06-30 14:23:50 [!!]     return storage_object[key]
2025-06-30 14:23:50 [!!]            ~~~~~~~~~~~~~~^^^^^
2025-06-30 14:23:50 [!!] KeyError: 'throttle'
None2025-06-30 14:23:50 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 14:23:50 [!!] Traceback (most recent call last):
2025-06-30 14:23:50 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
2025-06-30 14:23:50 [!!]     return getattr(self._connections, alias)
2025-06-30 14:23:50 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 14:23:50 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
2025-06-30 14:23:50 [!!]     return getattr(storage, key)
2025-06-30 14:23:50 [!!]            ^^^^^^^^^^^^^^^^^^^^^
2025-06-30 14:23:50 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
2025-06-30 14:23:50 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
2025-06-30 14:23:50 [!!] AttributeError: <asgiref.local._CVar object at 0x78713c50b830> object has no attribute 'throttle'
None2025-06-30 14:23:50 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 14:23:50 [!!] Traceback (most recent call last):
2025-06-30 14:23:50 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
2025-06-30 14:23:50 [!!]     self.storage = caches["throttle"]
2025-06-30 14:23:50 [!!]                    ~~~~~~^^^^^^^^^^^^
2025-06-30 14:23:50 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
2025-06-30 14:23:50 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
2025-06-30 14:23:50 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
2025-06-30 14:23:50 [!!] Throttle: Errors encountered; using default cache.
2025-06-30 14:23:50 [!!] Traceback (most recent call last):
2025-06-30 14:23:50 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
2025-06-30 14:23:50 [!!]     return storage_object[key]
2025-06-30 14:23:50 [!!]            ~~~~~~~~~~~~~~^^^^^
2025-06-30 14:23:50 [!!] KeyError: 'throttle'
None2025-06-30 14:23:50 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 14:23:50 [!!] Traceback (most recent call last):
2025-06-30 14:23:50 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
2025-06-30 14:23:50 [!!]     return getattr(self._connections, alias)
2025-06-30 14:23:50 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 14:23:50 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
2025-06-30 14:23:50 [!!]     return getattr(storage, key)
2025-06-30 14:23:50 [!!]            ^^^^^^^^^^^^^^^^^^^^^
2025-06-30 14:23:50 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
2025-06-30 14:23:50 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
2025-06-30 14:23:50 [!!] AttributeError: <asgiref.local._CVar object at 0x78713c50b830> object has no attribute 'throttle'
None2025-06-30 14:23:50 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 14:23:50 [!!] Traceback (most recent call last):
2025-06-30 14:23:50 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
2025-06-30 14:23:50 [!!]     self.storage = caches["throttle"]
2025-06-30 14:23:50 [!!]                    ~~~~~~^^^^^^^^^^^^
2025-06-30 14:23:50 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
2025-06-30 14:23:50 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
2025-06-30 14:23:50 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
2025-06-30 14:23:50 [!!] Throttle: Errors encountered; using default cache.
2025-06-30 14:23:51 [..] Loaded.
2025-06-30 14:23:51 [..] twistd 23.10.0 (/usr/bin/python3.12 3.12.3) starting up.
2025-06-30 14:23:51 [..] reactor class: twisted.internet.epollreactor.EPollReactor.
2025-06-30 14:23:51 [..] Webserver starting on 4005
2025-06-30 14:23:51 [..] Initial setup: Creating objects (Account #1 and Limbo room) ...
2025-06-30 14:23:51 [..] Handler registered: cultivationhandler in category 'cultivation'
2025-06-30 14:23:51 [..] Handler registered: combatskillhandler in category 'combat'
2025-06-30 14:23:51 [..] Handler registered: alchemyhandler in category 'alchemy'
2025-06-30 14:23:51 [..] Handler registered: karmahandler in category 'karma'
2025-06-30 14:23:51 [..] 使用全局AI配置
2025-06-30 14:23:52 [..] Handler registered: aidirectorhandler in category 'ai_director'
2025-06-30 14:23:52 [SS] Character Created: admin (Caller: admin(account 1), IP: None).
2025-06-30 14:23:52 [..] Initial setup: Running at_initial_setup() hook.
2025-06-30 14:23:53 [..] Initial setup: Gathering static resources using 'collectstatic'
None2025-06-30 14:23:54 [..] 0 static files copied to '/mnt/d/project/evennia/xiuxian_mud_new/server/.static', 201 unmodified.
2025-06-30 14:23:54 [..] Initial setup complete. Restarting Server once.
2025-06-30 14:23:54 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:23:54 [..] Evennia Server successfully started.
2025-06-30 14:23:54 [..] Webserver waiting for 1 requests ... 
2025-06-30 14:23:55 [..] Server disconnected from the portal.
2025-06-30 14:23:55 [..] Main loop terminated.
2025-06-30 14:23:55 [..] Server Shut Down.
2025-06-30 14:23:57 [..] Loaded.
2025-06-30 14:23:55 [..] Loading /home/<USER>/.local/lib/python3.12/site-packages/evennia/server/server.py...
2025-06-30 14:23:56 [!!] Traceback (most recent call last):
2025-06-30 14:23:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
2025-06-30 14:23:56 [!!]     return storage_object[key]
2025-06-30 14:23:56 [!!]            ~~~~~~~~~~~~~~^^^^^
2025-06-30 14:23:56 [!!] KeyError: 'throttle'
None2025-06-30 14:23:56 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 14:23:56 [!!] Traceback (most recent call last):
2025-06-30 14:23:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
2025-06-30 14:23:56 [!!]     return getattr(self._connections, alias)
2025-06-30 14:23:56 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 14:23:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
2025-06-30 14:23:56 [!!]     return getattr(storage, key)
2025-06-30 14:23:56 [!!]            ^^^^^^^^^^^^^^^^^^^^^
2025-06-30 14:23:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
2025-06-30 14:23:56 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
2025-06-30 14:23:56 [!!] AttributeError: <asgiref.local._CVar object at 0x7963a5259ac0> object has no attribute 'throttle'
None2025-06-30 14:23:56 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 14:23:56 [!!] Traceback (most recent call last):
2025-06-30 14:23:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
2025-06-30 14:23:56 [!!]     self.storage = caches["throttle"]
2025-06-30 14:23:56 [!!]                    ~~~~~~^^^^^^^^^^^^
2025-06-30 14:23:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
2025-06-30 14:23:56 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
2025-06-30 14:23:56 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
2025-06-30 14:23:56 [!!] Throttle: Errors encountered; using default cache.
2025-06-30 14:23:56 [!!] Traceback (most recent call last):
2025-06-30 14:23:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 19, in __getattr__
2025-06-30 14:23:56 [!!]     return storage_object[key]
2025-06-30 14:23:56 [!!]            ~~~~~~~~~~~~~~^^^^^
2025-06-30 14:23:56 [!!] KeyError: 'throttle'
None2025-06-30 14:23:56 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 14:23:56 [!!] Traceback (most recent call last):
2025-06-30 14:23:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 58, in __getitem__
2025-06-30 14:23:56 [!!]     return getattr(self._connections, alias)
2025-06-30 14:23:56 [!!]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 14:23:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 118, in __getattr__
2025-06-30 14:23:56 [!!]     return getattr(storage, key)
2025-06-30 14:23:56 [!!]            ^^^^^^^^^^^^^^^^^^^^^
2025-06-30 14:23:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/asgiref/local.py", line 21, in __getattr__
2025-06-30 14:23:56 [!!]     raise AttributeError(f"{self!r} object has no attribute {key!r}")
2025-06-30 14:23:56 [!!] AttributeError: <asgiref.local._CVar object at 0x7963a5259ac0> object has no attribute 'throttle'
None2025-06-30 14:23:56 [!!] During handling of the above exception, another exception occurred:
None2025-06-30 14:23:56 [!!] Traceback (most recent call last):
2025-06-30 14:23:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/evennia/server/throttle.py", line 40, in __init__
2025-06-30 14:23:56 [!!]     self.storage = caches["throttle"]
2025-06-30 14:23:56 [!!]                    ~~~~~~^^^^^^^^^^^^
2025-06-30 14:23:56 [!!]   File "/home/<USER>/.local/lib/python3.12/site-packages/django/utils/connection.py", line 61, in __getitem__
2025-06-30 14:23:56 [!!]     raise self.exception_class(f"The connection '{alias}' doesn't exist.")
2025-06-30 14:23:56 [!!] django.core.cache.backends.base.InvalidCacheBackendError: The connection 'throttle' doesn't exist.
2025-06-30 14:23:56 [!!] Throttle: Errors encountered; using default cache.
2025-06-30 14:23:57 [..] Loaded.
2025-06-30 14:23:57 [..] twistd 23.10.0 (/usr/bin/python3.12 3.12.3) starting up.
2025-06-30 14:23:57 [..] reactor class: twisted.internet.epollreactor.EPollReactor.
2025-06-30 14:23:57 [..] Webserver starting on 4005
2025-06-30 14:23:57 [..] Evennia Server successfully restarted in 'reset' mode.
2025-06-30 14:24:00 [..] Handler registered: cultivationhandler in category 'cultivation'
2025-06-30 14:24:00 [..] Handler registered: combatskillhandler in category 'combat'
2025-06-30 14:24:00 [..] Handler registered: alchemyhandler in category 'alchemy'
2025-06-30 14:24:00 [..] Handler registered: karmahandler in category 'karma'
2025-06-30 14:24:00 [..] 使用全局AI配置
2025-06-30 14:24:00 [..] Handler registered: aidirectorhandler in category 'ai_director'
2025-06-30 14:28:01 [SS] Password successfully changed for karlo(account None).
2025-06-30 14:28:01 [SS] Account Created: karlo(account 2) (IP: 127.0.0.1).
2025-06-30 14:28:01 [SS] Character Created: karlo (Caller: karlo(account 2), IP: 127.0.0.1).
2025-06-30 14:28:11 [SS] Authentication Success: karlo(account 2) (IP: 127.0.0.1).
2025-06-30 14:28:11 [..] Logged in: karlo(account 2) 127.0.0.1 (1 session(s) total)
2025-06-30 14:29:47 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:32:28 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:32:56 [EE] Overwriting existing registered function stopWordFilter
2025-06-30 14:32:57 [WW] Warning: Idmapper flush called more than once in 5.0 min interval. Check memory usage.
2025-06-30 14:33:14 [..] Logged out: karlo(account 2) 127.0.0.1 (0 sessions(s) remaining)
2025-06-30 14:36:09 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:37:48 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:38:49 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:39:11 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:39:11 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:41:35 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:42:38 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:45:10 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:45:25 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:45:26 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:49:09 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:50:32 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:51:55 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:52:30 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:53:36 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:53:41 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.
2025-06-30 14:54:29 [WW] ip_from_request: No valid IP address found in request. Using remote_addr.

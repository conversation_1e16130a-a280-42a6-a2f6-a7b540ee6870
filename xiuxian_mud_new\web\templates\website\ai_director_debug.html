{% extends "website/base.html" %}

{% block title %}AI导演调试 - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">AI导演系统调试</h1>
            
            <!-- 状态概览 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">系统状态</h5>
                        </div>
                        <div class="card-body">
                            <div id="status-info">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                    <p class="mt-2">加载中...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">性能统计</h5>
                        </div>
                        <div class="card-body">
                            <div id="performance-info">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                    <p class="mt-2">加载中...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 调试信息 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">详细调试信息</h5>
                            <button class="btn btn-primary btn-sm" onclick="refreshDebugInfo()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="debug-info">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                    <p class="mt-2">加载中...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- API测试 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">API测试</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <button class="btn btn-outline-primary btn-block" onclick="testApi('status')">
                                        测试状态API
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-primary btn-block" onclick="testApi('stats')">
                                        测试统计API
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-primary btn-block" onclick="testApi('debug')">
                                        测试调试API
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-success btn-block" onclick="refreshAll()">
                                        刷新全部
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <h6>API响应:</h6>
                                <pre id="api-response" class="bg-light p-3" style="height: 200px; overflow-y: auto;">
等待API调用...
                                </pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// API基础URL
const API_BASE = '/api/ai-director/';

// 格式化JSON显示
function formatJson(obj) {
    return JSON.stringify(obj, null, 2);
}

// 显示错误信息
function showError(message) {
    return `<div class="alert alert-danger">${message}</div>`;
}

// 测试API端点
async function testApi(endpoint) {
    const responseElement = document.getElementById('api-response');
    responseElement.textContent = `正在调用 ${endpoint} API...`;
    
    try {
        const response = await fetch(`${API_BASE}${endpoint}/`);
        const data = await response.json();
        responseElement.textContent = formatJson(data);
    } catch (error) {
        responseElement.textContent = `错误: ${error.message}`;
    }
}

// 加载状态信息
async function loadStatus() {
    try {
        const response = await fetch(`${API_BASE}status/`);
        const data = await response.json();
        
        if (data.success) {
            document.getElementById('status-info').innerHTML = `
                <table class="table table-sm">
                    <tr><td><strong>状态:</strong></td><td><span class="badge badge-success">${data.status}</span></td></tr>
                    <tr><td><strong>AI可用:</strong></td><td><span class="badge ${data.ai_available ? 'badge-success' : 'badge-danger'}">${data.ai_available ? '是' : '否'}</span></td></tr>
                    <tr><td><strong>版本:</strong></td><td>${data.version}</td></tr>
                    <tr><td><strong>在线玩家:</strong></td><td>${data.system?.online_players || 0}</td></tr>
                    <tr><td><strong>总对象数:</strong></td><td>${data.system?.total_objects || 0}</td></tr>
                </table>
            `;
            
            if (data.performance) {
                document.getElementById('performance-info').innerHTML = `
                    <table class="table table-sm">
                        <tr><td><strong>总决策数:</strong></td><td>${data.performance.total_decisions}</td></tr>
                        <tr><td><strong>平均响应时间:</strong></td><td>${data.performance.avg_response_time_ms.toFixed(1)}ms</td></tr>
                        <tr><td><strong>缓存命中率:</strong></td><td>${data.performance.cache_hit_rate.toFixed(1)}%</td></tr>
                        <tr><td><strong>缓存命中:</strong></td><td>${data.performance.cache_hits}</td></tr>
                        <tr><td><strong>缓存未命中:</strong></td><td>${data.performance.cache_misses}</td></tr>
                    </table>
                `;
            }
        } else {
            document.getElementById('status-info').innerHTML = showError(data.error);
            document.getElementById('performance-info').innerHTML = showError('无法加载性能数据');
        }
    } catch (error) {
        document.getElementById('status-info').innerHTML = showError(`加载失败: ${error.message}`);
        document.getElementById('performance-info').innerHTML = showError('加载失败');
    }
}

// 加载调试信息
async function loadDebugInfo() {
    try {
        const response = await fetch(`${API_BASE}debug/`);
        const data = await response.json();
        
        if (data.success) {
            document.getElementById('debug-info').innerHTML = `
                <div class="accordion" id="debugAccordion">
                    <div class="card">
                        <div class="card-header" id="aiDirectorHeader">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#aiDirectorCollapse">
                                    AI导演实例信息
                                </button>
                            </h6>
                        </div>
                        <div id="aiDirectorCollapse" class="collapse show" data-parent="#debugAccordion">
                            <div class="card-body">
                                <pre class="bg-light p-2">${formatJson(data.ai_director)}</pre>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="systemHeader">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#systemCollapse">
                                    系统信息
                                </button>
                            </h6>
                        </div>
                        <div id="systemCollapse" class="collapse" data-parent="#debugAccordion">
                            <div class="card-body">
                                <pre class="bg-light p-2">${formatJson(data.system)}</pre>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="runtimeHeader">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#runtimeCollapse">
                                    运行时信息
                                </button>
                            </h6>
                        </div>
                        <div id="runtimeCollapse" class="collapse" data-parent="#debugAccordion">
                            <div class="card-body">
                                <pre class="bg-light p-2">${formatJson(data.runtime)}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } else {
            document.getElementById('debug-info').innerHTML = showError(data.error);
        }
    } catch (error) {
        document.getElementById('debug-info').innerHTML = showError(`加载失败: ${error.message}`);
    }
}

// 刷新调试信息
function refreshDebugInfo() {
    document.getElementById('debug-info').innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <p class="mt-2">刷新中...</p>
        </div>
    `;
    loadDebugInfo();
}

// 刷新全部信息
function refreshAll() {
    loadStatus();
    refreshDebugInfo();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadStatus();
    loadDebugInfo();
    
    // 每30秒自动刷新状态
    setInterval(loadStatus, 30000);
});
</script>
{% endblock %}
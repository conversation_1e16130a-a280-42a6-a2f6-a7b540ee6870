#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演系统全局配置文件
统一管理所有AI相关的配置信息
"""

import os
from typing import Dict, Any, Optional

class AIConfig:
    """AI配置管理类"""
    
    # ===========================================
    # MindCraft API 配置
    # ===========================================
    
    # API基础配置
    MINDCRAFT_API_BASE = "https://api.mindcraft.com.cn/v1"
    MINDCRAFT_API_KEY = "'MC-94D4CC750E92436FB3FA51C9F41D03A9'"  # 请替换为实际的API密钥
    MINDCRAFT_MODEL = "deepseek-r1-free"
    
    # ===========================================
    # AI导演系统配置
    # ===========================================
    
    # 基础AI设置
    AI_SETTINGS = {
        'MAX_TOKENS': 4000,
        'TEMPERATURE': 0.7,
        'STREAM': False,
        'TOP_P': 0.9,
        'FREQUENCY_PENALTY': 0.0,
        'PRESENCE_PENALTY': 0.0,
    }
    
    # 系统控制配置
    AI_AVAILABLE = True              # 是否启用AI导演系统
    AI_USE_MOCK_CLIENT = False       # 是否使用模拟客户端（开发/演示模式）
    AI_DEBUG = True                  # 是否启用调试模式
    AI_LOG_DECISIONS = True          # 是否记录AI决策日志
    
    # ===========================================
    # 性能优化配置
    # ===========================================
    
    # 缓存配置
    AI_CACHE_ENABLED = True
    AI_CACHE_TIMEOUT = 300           # 缓存超时时间（秒）
    AI_CACHE_MAX_SIZE = 1000         # 最大缓存条目数
    
    # 请求限制配置
    AI_REQUEST_TIMEOUT = 30          # API请求超时时间（秒）
    AI_MAX_RETRIES = 3               # 最大重试次数
    AI_RETRY_DELAY = 1               # 重试延迟（秒）
    
    # 并发控制
    AI_MAX_CONCURRENT_REQUESTS = 5   # 最大并发请求数
    AI_RATE_LIMIT_PER_MINUTE = 60    # 每分钟最大请求数
    
    # ===========================================
    # 故事和剧情配置
    # ===========================================
    
    # 故事解析配置
    STORY_ANALYSIS_CONFIG = {
        'max_characters': 20,         # 最大角色数量
        'max_plot_points': 50,        # 最大剧情点数量
        'min_story_length': 100,      # 最小故事长度（字符）
        'max_story_length': 10000,    # 最大故事长度（字符）
    }
    
    # 决策生成配置
    DECISION_CONFIG = {
        'min_confidence': 0.3,        # 最小置信度阈值
        'max_decision_length': 500,   # 最大决策内容长度
        'decision_types': [           # 支持的决策类型
            'PLOT_ADVANCEMENT',
            'CHARACTER_INTERACTION', 
            'CONFLICT_ESCALATION',
            'SCENE_TRANSITION',
            'DIALOGUE_GENERATION',
            'ENVIRONMENT_CHANGE'
        ]
    }
    
    # ===========================================
    # 提示词模板配置
    # ===========================================
    
    # 系统提示词
    SYSTEM_PROMPTS = {
        'story_analysis': """你是一个专业的仙侠小说AI导演。请分析给定的故事大纲，提取关键信息：
- 故事标题
- 核心主题
- 主要冲突
- 关键角色
- 重要剧情点
请以JSON格式返回结构化数据。""",
        
        'decision_making': """你是一个仙侠游戏的AI导演。根据当前游戏事件，生成合适的剧情决策：
- 分析事件的重要性和影响
- 考虑角色发展和故事推进
- 生成符合仙侠世界观的响应
- 确保决策有助于提升游戏体验""",
        
        'character_interaction': """你是仙侠世界的AI导演。为角色互动生成自然的对话和行为：
- 保持角色个性一致
- 符合仙侠文化背景
- 推进剧情发展
- 增强沉浸感"""
    }
    
    # ===========================================
    # 环境变量支持
    # ===========================================
    
    @classmethod
    def load_from_env(cls) -> None:
        """从环境变量加载配置"""
        # API配置
        cls.MINDCRAFT_API_KEY = os.getenv('MINDCRAFT_API_KEY', cls.MINDCRAFT_API_KEY)
        cls.MINDCRAFT_API_BASE = os.getenv('MINDCRAFT_API_BASE', cls.MINDCRAFT_API_BASE)
        cls.MINDCRAFT_MODEL = os.getenv('MINDCRAFT_MODEL', cls.MINDCRAFT_MODEL)
        
        # 系统配置
        cls.AI_AVAILABLE = os.getenv('AI_AVAILABLE', str(cls.AI_AVAILABLE)).lower() == 'true'
        cls.AI_USE_MOCK_CLIENT = os.getenv('AI_USE_MOCK_CLIENT', str(cls.AI_USE_MOCK_CLIENT)).lower() == 'true'
        cls.AI_DEBUG = os.getenv('AI_DEBUG', str(cls.AI_DEBUG)).lower() == 'true'
        
        # 性能配置
        cls.AI_CACHE_TIMEOUT = int(os.getenv('AI_CACHE_TIMEOUT', cls.AI_CACHE_TIMEOUT))
        cls.AI_REQUEST_TIMEOUT = int(os.getenv('AI_REQUEST_TIMEOUT', cls.AI_REQUEST_TIMEOUT))
        cls.AI_MAX_RETRIES = int(os.getenv('AI_MAX_RETRIES', cls.AI_MAX_RETRIES))
    
    @classmethod
    def get_api_config(cls) -> Dict[str, Any]:
        """获取API配置"""
        return {
            'api_base': cls.MINDCRAFT_API_BASE,
            'api_key': cls.MINDCRAFT_API_KEY,
            'model': cls.MINDCRAFT_MODEL,
            'timeout': cls.AI_REQUEST_TIMEOUT,
            'max_retries': cls.AI_MAX_RETRIES,
            'retry_delay': cls.AI_RETRY_DELAY,
            **cls.AI_SETTINGS
        }
    
    @classmethod
    def get_cache_config(cls) -> Dict[str, Any]:
        """获取缓存配置"""
        return {
            'enabled': cls.AI_CACHE_ENABLED,
            'timeout': cls.AI_CACHE_TIMEOUT,
            'max_size': cls.AI_CACHE_MAX_SIZE
        }
    
    @classmethod
    def get_story_config(cls) -> Dict[str, Any]:
        """获取故事配置"""
        return {
            'analysis': cls.STORY_ANALYSIS_CONFIG,
            'decision': cls.DECISION_CONFIG,
            'prompts': cls.SYSTEM_PROMPTS
        }
    
    @classmethod
    def is_available(cls) -> bool:
        """检查AI系统是否可用"""
        if cls.AI_USE_MOCK_CLIENT:
            return True
        return cls.AI_AVAILABLE and bool(cls.MINDCRAFT_API_KEY and cls.MINDCRAFT_API_KEY != "sk-mindcraft-api-key-here")
    
    @classmethod
    def validate_config(cls) -> Dict[str, Any]:
        """验证配置有效性"""
        issues = []
        warnings = []
        
        # 检查API密钥
        if not cls.AI_USE_MOCK_CLIENT:
            if not cls.MINDCRAFT_API_KEY or cls.MINDCRAFT_API_KEY == "sk-mindcraft-api-key-here":
                issues.append("MINDCRAFT_API_KEY 未设置或使用默认值")
            
            if not cls.MINDCRAFT_API_BASE:
                issues.append("MINDCRAFT_API_BASE 未设置")
            
            if not cls.MINDCRAFT_MODEL:
                issues.append("MINDCRAFT_MODEL 未设置")
        
        # 检查性能配置
        if cls.AI_CACHE_TIMEOUT < 60:
            warnings.append("缓存超时时间过短，可能影响性能")
        
        if cls.AI_REQUEST_TIMEOUT < 10:
            warnings.append("请求超时时间过短，可能导致请求失败")
        
        if cls.AI_MAX_CONCURRENT_REQUESTS > 10:
            warnings.append("并发请求数过高，可能触发API限制")
        
        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'warnings': warnings,
            'config_summary': {
                'api_available': cls.is_available(),
                'mock_mode': cls.AI_USE_MOCK_CLIENT,
                'debug_mode': cls.AI_DEBUG,
                'cache_enabled': cls.AI_CACHE_ENABLED
            }
        }
    
    @classmethod
    def print_config_summary(cls) -> None:
        """打印配置摘要"""
        validation = cls.validate_config()

        print("AI导演系统配置摘要")
        print("=" * 50)
        print(f"API可用性: {'可用' if validation['config_summary']['api_available'] else '不可用'}")
        print(f"运行模式: {'模拟模式' if validation['config_summary']['mock_mode'] else '生产模式'}")
        print(f"调试模式: {'启用' if validation['config_summary']['debug_mode'] else '禁用'}")
        print(f"缓存状态: {'启用' if validation['config_summary']['cache_enabled'] else '禁用'}")
        print(f"API模型: {cls.MINDCRAFT_MODEL}")
        print(f"最大令牌: {cls.AI_SETTINGS['MAX_TOKENS']}")
        print(f"温度参数: {cls.AI_SETTINGS['TEMPERATURE']}")

        if validation['issues']:
            print("\n配置问题:")
            for issue in validation['issues']:
                print(f"  - {issue}")

        if validation['warnings']:
            print("\n配置警告:")
            for warning in validation['warnings']:
                print(f"  - {warning}")

        if validation['valid']:
            print("\n配置验证通过")
        else:
            print("\n配置验证失败，请修复上述问题")


# 自动加载环境变量配置
AIConfig.load_from_env()

# 导出常用配置
API_CONFIG = AIConfig.get_api_config()
CACHE_CONFIG = AIConfig.get_cache_config()
STORY_CONFIG = AIConfig.get_story_config()

# 兼容性别名
MINDCRAFT_API_KEY = AIConfig.MINDCRAFT_API_KEY
MINDCRAFT_API_BASE = AIConfig.MINDCRAFT_API_BASE
MINDCRAFT_MODEL = AIConfig.MINDCRAFT_MODEL
AI_AVAILABLE = AIConfig.AI_AVAILABLE
AI_USE_MOCK_CLIENT = AIConfig.AI_USE_MOCK_CLIENT

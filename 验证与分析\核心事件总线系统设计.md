# 核心事件总线系统设计

## 1. 系统概述

### 1.1 设计理念
事件总线系统是AI导演驱动架构的核心基础设施，为整个修仙世界提供实时、高性能的事件通信和响应机制。

### 1.2 核心特性
- **毫秒级响应**：基于Evennia的in-game Python实现零延迟事件处理
- **AI导演友好**：为AI决策提供结构化、语义化的事件数据
- **无限扩展**：支持动态事件类型注册和Handler模式组件化
- **智能过滤**：TagProperty支持的高性能事件筛选和路由
- **持久化保证**：事件日志和状态恢复机制

## 2. 事件类型定义

### 2.1 修仙系统事件

#### 境界突破事件 (CultivationBreakthroughEvent)
```python
class CultivationBreakthroughEvent(BaseEvent):
    event_type = "cultivation.breakthrough"
    
    @property
    def data_schema(self):
        return {
            "character_id": str,
            "old_realm": str,           # 旧境界：练气三层
            "new_realm": str,           # 新境界：练气四层
            "breakthrough_type": str,   # 突破类型：normal/tribulation/epiphany
            "cultivation_base": int,    # 修为数值
            "spiritual_power": int,     # 灵力变化
            "comprehension": float,     # 悟性增长
            "location": str,            # 突破地点
            "timestamp": datetime,
            "triggers": list,           # 触发条件：["丹药助力", "天时地利"]
            "consequences": dict        # 后果影响：{"attr_boost": {}, "unlocked_skills": []}
        }
    
    @property
    def ai_context(self):
        """为AI导演提供的语义化描述"""
        return {
            "significance": self.calculate_breakthrough_significance(),
            "narrative_impact": "moderate" if self.data["old_realm"].split("层")[0] == self.data["new_realm"].split("层")[0] else "major",
            "story_hooks": self.generate_story_opportunities(),
            "world_reactions": self.predict_world_responses()
        }
```

#### 修炼进度事件 (CultivationProgressEvent)
```python
class CultivationProgressEvent(BaseEvent):
    event_type = "cultivation.progress"
    
    @property
    def data_schema(self):
        return {
            "character_id": str,
            "technique_name": str,      # 修炼功法：《青云诀》
            "progress_delta": int,      # 进度变化
            "current_progress": int,    # 当前进度
            "max_progress": int,        # 最大进度
            "technique_level": int,     # 功法层数
            "efficiency": float,        # 修炼效率：0.8-1.5
            "location_bonus": float,    # 地点加成
            "resource_consumed": dict,  # 消耗资源：{"灵石": 10, "丹药": 1}
            "insights_gained": list,    # 感悟获得：["气息调和", "经脉顺畅"]
            "bottleneck_approached": bool, # 是否接近瓶颈
            "timestamp": datetime
        }
```

#### 天象异常事件 (CelestialAnomalyEvent)
```python
class CelestialAnomalyEvent(BaseEvent):
    event_type = "world.celestial_anomaly"
    
    @property
    def data_schema(self):
        return {
            "anomaly_type": str,        # 异象类型：blood_moon/meteor_shower/aurora
            "intensity": str,           # 强度：minor/moderate/major/cataclysmic
            "affected_regions": list,   # 影响区域：["青云门", "万兽林"]
            "duration": int,            # 持续时间（秒）
            "cultivation_effects": dict, # 修炼影响：{"boost": 1.2, "risk": 0.1}
            "spiritual_fluctuations": dict, # 灵气波动
            "prophecy_connections": list,   # 预言关联
            "ai_generated": bool,       # 是否AI生成
            "story_significance": str,  # 剧情重要性
            "timestamp": datetime
        }
```

### 2.2 战斗系统事件

#### 技能释放事件 (SkillCastEvent)
```python
class SkillCastEvent(BaseEvent):
    event_type = "combat.skill_cast"
    
    @property
    def data_schema(self):
        return {
            "caster_id": str,
            "target_ids": list,         # 目标列表，支持群攻
            "skill_name": str,          # 技能名称：《青云剑诀》
            "skill_level": int,         # 技能等级
            "skill_type": str,          # 技能类型：attack/defense/heal/buff
            "damage_dealt": int,        # 造成伤害
            "mana_consumed": int,       # 消耗灵力
            "critical_hit": bool,       # 是否暴击
            "elemental_type": str,      # 属性类型：fire/water/earth/metal/wood
            "combo_sequence": int,      # 连击序号
            "battlefield_effects": dict, # 战场效果变化
            "timestamp": datetime
        }
```

#### 战斗状态事件 (CombatStateEvent)
```python
class CombatStateEvent(BaseEvent):
    event_type = "combat.state_change"
    
    @property
    def data_schema(self):
        return {
            "character_id": str,
            "old_state": str,           # 旧状态：normal/poisoned/stunned
            "new_state": str,           # 新状态
            "state_source": str,        # 状态来源：skill/item/environment
            "duration": int,            # 持续时间
            "intensity": float,         # 状态强度
            "can_be_dispelled": bool,   # 能否驱散
            "stacking_behavior": str,   # 叠加行为：stack/refresh/replace
            "visual_effects": str,      # 视觉效果描述
            "timestamp": datetime
        }
```

### 2.3 社交系统事件

#### 门派冲突事件 (SectConflictEvent)
```python
class SectConflictEvent(BaseEvent):
    event_type = "social.sect_conflict"
    
    @property
    def data_schema(self):
        return {
            "aggressor_sect": str,      # 挑起方门派
            "defender_sect": str,       # 应战方门派
            "conflict_type": str,       # 冲突类型：resource/honor/territory/revenge
            "conflict_scale": str,      # 冲突规模：skirmish/battle/war
            "participants": list,       # 参与者ID列表
            "casualties": dict,         # 伤亡统计
            "resource_stakes": dict,    # 争夺资源
            "honor_impact": dict,       # 声誉影响
            "territorial_changes": dict, # 领土变化
            "resolution": str,          # 解决方式：victory/defeat/truce/ongoing
            "consequences": dict,       # 后续影响
            "timestamp": datetime
        }
```

#### 师父弟子事件 (MasterDiscipleEvent)
```python
class MasterDiscipleEvent(BaseEvent):
    event_type = "social.master_disciple"
    
    @property
    def data_schema(self):
        return {
            "master_id": str,
            "disciple_id": str,
            "interaction_type": str,    # 交互类型：teaching/punishment/reward/ceremony
            "skill_transmitted": str,   # 传授技能
            "knowledge_shared": str,    # 分享知识
            "relationship_change": float, # 关系变化：-1.0到1.0
            "ceremony_type": str,       # 仪式类型：apprentice/graduation/inheritance
            "witness_count": int,       # 见证人数
            "location_significance": str, # 地点重要性
            "gifts_exchanged": list,    # 交换物品
            "timestamp": datetime
        }
```

### 2.4 世界环境事件

#### 灵气变化事件 (SpiritualEnergyEvent)
```python
class SpiritualEnergyEvent(BaseEvent):
    event_type = "world.spiritual_energy"
    
    @property
    def data_schema(self):
        return {
            "location_id": str,
            "old_concentration": float,  # 旧灵气浓度
            "new_concentration": float,  # 新灵气浓度
            "change_rate": float,       # 变化速率
            "change_source": str,       # 变化原因：natural/artifact/cultivation/depletion
            "element_distribution": dict, # 五行分布：{"fire": 0.2, "water": 0.3, ...}
            "stability": float,         # 稳定性：0-1
            "cultivation_efficiency": float, # 修炼效率影响
            "affected_radius": int,     # 影响半径（格）
            "duration": int,            # 持续时间
            "side_effects": list,       # 副作用
            "timestamp": datetime
        }
```

## 3. 事件总线架构

### 3.1 核心组件设计

#### EventBus 核心类
```python
class XianxiaEventBus(DefaultScript):
    """
    仙侠MUD事件总线核心
    继承自Evennia的Script系统，确保持久化和自动恢复
    """
    
    def at_script_creation(self):
        self.key = "xianxia_event_bus"
        self.desc = "仙侠修仙世界事件总线系统"
        self.interval = 0.1  # 100ms检查周期，确保实时性
        self.persistent = True
        self.start_delay = False
        
        # 事件队列和处理器注册表
        self.db.event_queue = []
        self.db.handlers = {}
        self.db.event_filters = {}
        self.db.performance_metrics = {
            "events_processed": 0,
            "average_latency": 0.0,
            "peak_queue_size": 0
        }
    
    def publish_event(self, event):
        """发布事件到总线"""
        start_time = time.time()
        
        # 事件验证和增强
        validated_event = self.validate_and_enhance_event(event)
        
        # 添加到队列
        self.db.event_queue.append({
            "event": validated_event,
            "timestamp": start_time,
            "priority": self.calculate_priority(validated_event)
        })
        
        # 性能监控
        self.update_performance_metrics(start_time)
        
        # 立即处理高优先级事件
        if validated_event.priority >= EventPriority.HIGH:
            self.process_events()
    
    def subscribe(self, handler, event_types=None, filters=None):
        """订阅事件处理器"""
        handler_id = f"{handler.__class__.__name__}_{id(handler)}"
        
        self.db.handlers[handler_id] = {
            "handler": handler,
            "event_types": event_types or ["*"],
            "filters": filters or {},
            "performance": {"processed": 0, "errors": 0}
        }
        
        return handler_id
    
    def at_repeat(self):
        """定期处理事件队列"""
        self.process_events()
        self.cleanup_expired_events()
        self.monitor_system_health()
    
    def process_events(self):
        """处理事件队列"""
        if not self.db.event_queue:
            return
        
        # 按优先级排序
        self.db.event_queue.sort(key=lambda x: x["priority"], reverse=True)
        
        processed_count = 0
        max_batch_size = 50  # 单次最多处理50个事件
        
        while self.db.event_queue and processed_count < max_batch_size:
            event_data = self.db.event_queue.pop(0)
            event = event_data["event"]
            
            # 分发给匹配的处理器
            self.dispatch_to_handlers(event)
            
            # AI导演特殊处理
            if event.involves_ai_director:
                self.notify_ai_director(event)
            
            processed_count += 1
        
        # 更新性能指标
        self.db.performance_metrics["events_processed"] += processed_count
    
    def dispatch_to_handlers(self, event):
        """将事件分发给匹配的处理器"""
        for handler_id, handler_info in self.db.handlers.items():
            try:
                if self.event_matches_handler(event, handler_info):
                    handler = handler_info["handler"]
                    
                    # 异步处理避免阻塞
                    utils.delay(0.01, handler.handle_event, event)
                    
                    # 记录处理统计
                    handler_info["performance"]["processed"] += 1
                    
            except Exception as e:
                handler_info["performance"]["errors"] += 1
                logger.log_error(f"Event handler {handler_id} failed: {e}")
```

#### EventHandler 基类
```python
class BaseEventHandler:
    """事件处理器基类"""
    
    def __init__(self, priority=EventPriority.NORMAL):
        self.priority = priority
        self.event_bus = None
        self.filters = {}
        
    def initialize(self, event_bus):
        """初始化处理器"""
        self.event_bus = event_bus
        self.setup_filters()
        self.register_to_bus()
    
    def handle_event(self, event):
        """处理事件 - 子类必须实现"""
        raise NotImplementedError("Subclasses must implement handle_event")
    
    def setup_filters(self):
        """设置事件过滤器"""
        pass
    
    def register_to_bus(self):
        """注册到事件总线"""
        self.handler_id = self.event_bus.subscribe(
            self, 
            event_types=self.get_supported_event_types(),
            filters=self.filters
        )
    
    def get_supported_event_types(self):
        """返回支持的事件类型列表"""
        return ["*"]
```

### 3.2 AI导演集成

#### AI导演事件处理器
```python
class AIDirectorEventHandler(BaseEventHandler):
    """AI导演专用事件处理器"""
    
    def __init__(self):
        super().__init__(priority=EventPriority.HIGH)
        self.narrative_state = {}
        self.decision_queue = []
        self.context_buffer = []
    
    def handle_event(self, event):
        """AI导演事件处理逻辑"""
        # 更新叙事状态
        self.update_narrative_state(event)
        
        # 评估剧情影响
        impact_level = self.assess_narrative_impact(event)
        
        if impact_level >= NarrativeImpact.SIGNIFICANT:
            # 需要AI决策的重要事件
            self.queue_ai_decision(event)
        
        # 更新上下文缓冲区
        self.update_context_buffer(event)
        
        # 检查是否需要触发剧情节点
        self.check_story_triggers(event)
    
    def assess_narrative_impact(self, event):
        """评估事件的叙事影响"""
        if isinstance(event, CultivationBreakthroughEvent):
            realm_tier = self.parse_realm_tier(event.data["new_realm"])
            if realm_tier >= RealmTier.FOUNDATION_ESTABLISHMENT:
                return NarrativeImpact.MAJOR
            return NarrativeImpact.MODERATE
            
        elif isinstance(event, SectConflictEvent):
            if event.data["conflict_scale"] in ["battle", "war"]:
                return NarrativeImpact.MAJOR
            return NarrativeImpact.SIGNIFICANT
            
        elif isinstance(event, CelestialAnomalyEvent):
            intensity_map = {
                "minor": NarrativeImpact.MINOR,
                "moderate": NarrativeImpact.MODERATE,
                "major": NarrativeImpact.MAJOR,
                "cataclysmic": NarrativeImpact.CRITICAL
            }
            return intensity_map.get(event.data["intensity"], NarrativeImpact.MINOR)
        
        return NarrativeImpact.MINOR
    
    def queue_ai_decision(self, event):
        """将需要AI决策的事件加入队列"""
        decision_context = {
            "event": event,
            "current_narrative": self.get_current_narrative_summary(),
            "player_patterns": self.analyze_recent_player_behavior(),
            "story_state": self.get_story_state(),
            "priority": self.calculate_decision_priority(event)
        }
        
        self.decision_queue.append(decision_context)
        
        # 立即处理高优先级决策
        if decision_context["priority"] >= DecisionPriority.URGENT:
            utils.delay(0.1, self.process_ai_decision, decision_context)
```

### 3.3 性能优化

#### TagProperty事件过滤器
```python
class TagPropertyEventFilter:
    """基于TagProperty的高性能事件过滤器"""
    
    def __init__(self):
        self.filter_cache = {}
        self.tag_indexes = {}
    
    def add_filter(self, handler_id, filter_spec):
        """添加过滤器规则"""
        optimized_filter = self.optimize_filter(filter_spec)
        self.filter_cache[handler_id] = optimized_filter
        
        # 构建Tag索引
        self.build_tag_indexes(optimized_filter)
    
    def matches_filter(self, event, handler_id):
        """检查事件是否匹配处理器过滤器"""
        if handler_id not in self.filter_cache:
            return True
        
        filter_spec = self.filter_cache[handler_id]
        
        # 使用Tag索引快速过滤
        for tag_condition in filter_spec.get("tags", []):
            if not self.check_tag_condition(event, tag_condition):
                return False
        
        # 属性范围过滤
        for attr_condition in filter_spec.get("attributes", []):
            if not self.check_attribute_condition(event, attr_condition):
                return False
        
        return True
    
    def optimize_filter(self, filter_spec):
        """优化过滤器以提高查询性能"""
        # 将字符串条件转换为编译后的正则表达式
        # 将范围条件转换为快速比较函数
        # 构建Tag查询的复合索引
        return optimized_spec
```

## 4. 实现计划

### 4.1 第一阶段：核心框架 (3天)
- 实现BaseEvent和EventBus核心类
- 创建基础事件类型定义
- 集成Evennia的Script系统
- 基础的事件发布和订阅机制

### 4.2 第二阶段：修仙事件 (2天)
- 实现修仙系统相关事件类型
- 创建CultivationHandler等专用处理器
- 与现有修仙系统集成
- 性能测试和优化

### 4.3 第三阶段：AI导演集成 (2天)
- 实现AIDirectorEventHandler
- 创建叙事影响评估系统
- 集成LLM决策机制
- 测试AI响应延迟

### 4.4 第四阶段：优化扩展 (1天)
- TagProperty过滤器实现
- 性能监控和指标收集
- 错误处理和恢复机制
- 文档和测试补充

## 5. 验证指标

### 5.1 性能指标
- 事件处理延迟：< 10ms (p99)
- 队列吞吐量：> 1000 events/second
- 内存使用：< 100MB for 10k events
- 错误恢复时间：< 1 second

### 5.2 功能指标
- 事件类型覆盖率：100% 核心玩法
- AI导演响应率：> 95% 重要事件
- 处理器注册成功率：100%
- 数据持久化完整性：100%

这个事件总线系统将成为整个AI导演驱动架构的神经中枢，为创造真正智能和响应式的修仙世界奠定坚实基础。
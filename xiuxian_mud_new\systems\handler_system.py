# Handler生态组件化框架
# 基于设计文档实现的@lazy_property模式和70%+内存优化

import time
import threading
import weakref
from functools import wraps
from typing import Dict, List, Any, Optional, Type, Callable
from collections import defaultdict
from enum import Enum

try:
    from evennia import logger
    if logger is None:
        logger = None
except ImportError:
    logger = None


class HandlerEventType(Enum):
    """Handler事件类型"""
    HANDLER_CREATED = "handler_created"
    HANDLER_DESTROYED = "handler_destroyed"
    HANDLER_STATE_CHANGED = "handler_state_changed"
    HANDLER_DATA_UPDATED = "handler_data_updated"
    HANDLER_DEPENDENCY_ADDED = "handler_dependency_added"
    HANDLER_DEPENDENCY_REMOVED = "handler_dependency_removed"


class HandlerEvent:
    """Handler事件"""

    def __init__(self, event_type: HandlerEventType, source_handler: str,
                 target_handler: str = None, data: dict = None):
        self.event_type = event_type
        self.source_handler = source_handler
        self.target_handler = target_handler
        self.data = data or {}
        self.timestamp = time.time()
        self.event_id = f"{event_type.value}_{int(self.timestamp * 1000)}"


class lazy_property:
    """
    延迟加载属性装饰器
    只在首次访问时创建对象，并提供自动回收机制
    """
    
    def __init__(self, func):
        self.func = func
        self.name = func.__name__
        self.cache_key = f"_lazy_{self.name}"
        
    def __get__(self, obj, cls):
        if obj is None:
            return self

        # 检查缓存
        if hasattr(obj, self.cache_key):
            cached_value = getattr(obj, self.cache_key)
            if cached_value is not None:
                # 记录访问
                HandlerMemoryManager.record_handler_access(obj, self.name)
                return cached_value

        # 创建新实例
        value = self.func(obj)
        setattr(obj, self.cache_key, value)

        # 注册到内存管理器
        HandlerMemoryManager.register_handler(obj, self.name, value)

        # 发布Handler创建事件
        if hasattr(value, 'publish_event'):
            value.publish_event(HandlerEventType.HANDLER_CREATED)

        return value
    
    def __set__(self, obj, value):
        setattr(obj, self.cache_key, value)
    
    def __delete__(self, obj):
        if hasattr(obj, self.cache_key):
            # 清理Handler
            old_value = getattr(obj, self.cache_key)
            if hasattr(old_value, 'cleanup'):
                old_value.cleanup()
            
            delattr(obj, self.cache_key)
            HandlerMemoryManager.unregister_handler(obj, self.name)


class BaseHandler:
    """
    Handler基类 - 所有功能模块的基础
    """
    
    def __init__(self, owner):
        self.owner = weakref.ref(owner)  # 弱引用避免循环引用
        self.is_active = False
        self.last_access_time = time.time()
        self.initialization_data = {}
        self.dependencies = []
        self.dependents = []
        
        # 性能统计
        self.stats = {
            "access_count": 0,
            "memory_usage": 0,
            "last_cleanup": time.time()
        }
        
        # 初始化Handler
        self.initialize()
    
    def initialize(self):
        """初始化Handler - 子类可重写"""
        self.is_active = True
        # 避免在初始化过程中递归调用日志
        try:
            self.log_info(f"{self.__class__.__name__} initialized")
        except (RecursionError, RuntimeError):
            # 静默失败，避免初始化崩溃
            pass
    
    def cleanup(self):
        """清理Handler资源"""
        self.is_active = False
        self.clear_dependencies()
        self.log_info(f"{self.__class__.__name__} cleaned up")
    
    def access(self):
        """记录访问时间和统计"""
        self.last_access_time = time.time()
        self.stats["access_count"] += 1
    
    def add_dependency(self, handler_name: str):
        """添加依赖关系"""
        if handler_name not in self.dependencies:
            self.dependencies.append(handler_name)
            
            # 通知依赖的Handler
            owner = self.owner()
            if owner and hasattr(owner, f"get_handler"):
                dependent_handler = owner.get_handler(handler_name)
                if dependent_handler and self not in dependent_handler.dependents:
                    dependent_handler.dependents.append(self)
    
    def clear_dependencies(self):
        """清理依赖关系"""
        for dependent in self.dependents:
            try:
                dependent.remove_dependency(self.__class__.__name__.lower())
            except:
                pass
        
        self.dependencies.clear()
        self.dependents.clear()
    
    def get_owner(self):
        """获取拥有者对象"""
        return self.owner()
    
    def log_info(self, message: str):
        """日志记录"""
        try:
            owner = self.get_owner()
            owner_name = owner.key if owner and hasattr(owner, 'key') else "Unknown"
            log_msg = f"[Handler] {owner_name}.{self.__class__.__name__}: {message}"
            if logger:
                logger.log_info(log_msg)
            else:
                print(log_msg)
        except (RecursionError, RuntimeError):
            # 防止递归调用，静默失败
            pass
    
    def publish_event(self, event_type: HandlerEventType, target_handler: str = None, data: dict = None):
        """发布Handler事件"""
        event = HandlerEvent(event_type, self.__class__.__name__, target_handler, data)
        HandlerCommunicationBus.publish_event(event)

    def subscribe_to_events(self, event_type: HandlerEventType, callback: Callable):
        """订阅事件"""
        HandlerCommunicationBus.subscribe(event_type, self.__class__.__name__, callback)

    def communicate_with_handler(self, handler_name: str, message_type: str, data: dict = None):
        """与其他Handler通信"""
        self.publish_event(
            HandlerEventType.HANDLER_DATA_UPDATED,
            handler_name,
            {"message_type": message_type, "data": data or {}}
        )

    def get_handler_communication_history(self) -> List[HandlerEvent]:
        """获取与此Handler相关的通信历史"""
        return HandlerCommunicationBus.get_event_history(self.__class__.__name__)

    def serialize_state(self) -> dict:
        """序列化Handler状态"""
        return {
            "class_name": self.__class__.__name__,
            "is_active": self.is_active,
            "initialization_data": self.initialization_data,
            "dependencies": self.dependencies,
            "stats": self.stats
        }
    
    def deserialize_state(self, state_data: dict):
        """反序列化Handler状态"""
        self.is_active = state_data.get("is_active", False)
        self.initialization_data = state_data.get("initialization_data", {})
        self.dependencies = state_data.get("dependencies", [])
        self.stats = state_data.get("stats", {})


class HandlerRegistry:
    """
    Handler注册中心 - 管理所有可用的Handler类型
    """
    
    _handlers: Dict[str, Type[BaseHandler]] = {}
    _categories: Dict[str, List[str]] = defaultdict(list)
    
    @classmethod
    def register(cls, handler_class: Type[BaseHandler], category: str = "general"):
        """注册Handler类"""
        handler_name = handler_class.__name__.lower()
        cls._handlers[handler_name] = handler_class
        cls._categories[category].append(handler_name)
        
        log_msg = f"Handler registered: {handler_name} in category '{category}'"
        if logger:
            logger.log_info(log_msg)
        else:
            print(log_msg)
    
    @classmethod
    def get_handler_class(cls, handler_name: str) -> Optional[Type[BaseHandler]]:
        """获取Handler类"""
        return cls._handlers.get(handler_name.lower())
    
    @classmethod
    def get_available_handlers(cls, category: str = None) -> List[str]:
        """获取可用的Handler列表"""
        if category:
            return cls._categories.get(category, [])
        return list(cls._handlers.keys())
    
    @classmethod
    def create_handler(cls, handler_name: str, owner) -> Optional[BaseHandler]:
        """创建Handler实例"""
        handler_class = cls.get_handler_class(handler_name)
        if handler_class:
            return handler_class(owner)
        return None


class HandlerMemoryManager:
    """
    Handler内存管理器 - 实现70%+内存优化的核心组件
    """

    _active_handlers: Dict[int, Dict[str, BaseHandler]] = defaultdict(dict)
    _access_times: Dict[int, Dict[str, float]] = defaultdict(dict)
    _memory_stats: Dict[int, Dict[str, dict]] = defaultdict(dict)

    # 优化配置
    _cleanup_thresholds = {
        "idle_time": 1800,  # 30分钟未使用自动清理
        "memory_limit": 100 * 1024 * 1024,  # 100MB内存限制
        "handler_count": 1000  # 最大Handler数量
    }

    # 统计数据
    _optimization_stats = {
        "total_created": 0,
        "total_cleaned": 0,
        "memory_saved": 0,
        "cache_hits": 0,
        "cache_misses": 0,
        "last_cleanup": time.time()
    }

    @classmethod
    def register_handler(cls, owner, handler_name: str, handler: BaseHandler):
        """注册Handler到内存管理"""
        owner_id = id(owner)
        cls._active_handlers[owner_id][handler_name] = handler
        cls._access_times[owner_id][handler_name] = time.time()

        # 更新内存统计
        cls._update_memory_stats(owner_id, handler_name, handler)
        cls._optimization_stats["total_created"] += 1

        # 检查内存压力
        cls._check_memory_pressure()

    @classmethod
    def _update_memory_stats(cls, owner_id: int, handler_name: str, handler: BaseHandler):
        """更新内存使用统计"""
        estimated_memory = cls._estimate_handler_memory(handler)

        cls._memory_stats[owner_id][handler_name] = {
            "created_time": time.time(),
            "access_count": 1,
            "estimated_memory": estimated_memory,
            "handler_type": handler.__class__.__name__,
            "last_cleanup": time.time()
        }

    @classmethod
    def _estimate_handler_memory(cls, handler: BaseHandler) -> int:
        """估算Handler内存使用"""
        base_size = 1024  # 基础Handler大小

        # 根据Handler类型调整估算
        handler_type = handler.__class__.__name__
        type_multipliers = {
            "CultivationHandler": 2.0,  # 修仙数据较多
            "AIDirectorHandler": 3.0,   # AI数据较大
            "CombatSkillHandler": 1.5,  # 技能数据中等
            "AlchemyHandler": 1.8,      # 配方数据较多
            "KarmaHandler": 1.2         # 因果数据较少
        }

        multiplier = type_multipliers.get(handler_type, 1.0)
        return int(base_size * multiplier)

    @classmethod
    def _check_memory_pressure(cls):
        """检查内存压力并执行清理"""
        total_memory = cls.get_total_memory_usage()
        total_handlers = cls.get_total_handler_count()

        # 内存压力检查
        if (total_memory > cls._cleanup_thresholds["memory_limit"] or
            total_handlers > cls._cleanup_thresholds["handler_count"]):

            cleaned = cls.cleanup_inactive_handlers(
                max_idle_time=cls._cleanup_thresholds["idle_time"] // 2
            )

            if cleaned > 0:
                cls._log_info(f"内存压力清理: 清理了 {cleaned} 个Handler")

    @classmethod
    def record_handler_access(cls, owner, handler_name: str):
        """记录Handler访问"""
        owner_id = id(owner)
        if owner_id in cls._access_times and handler_name in cls._access_times[owner_id]:
            cls._access_times[owner_id][handler_name] = time.time()

            # 更新访问计数
            if owner_id in cls._memory_stats and handler_name in cls._memory_stats[owner_id]:
                cls._memory_stats[owner_id][handler_name]["access_count"] += 1
                cls._optimization_stats["cache_hits"] += 1
        else:
            cls._optimization_stats["cache_misses"] += 1

    @classmethod
    def unregister_handler(cls, owner, handler_name: str):
        """从内存管理中注销Handler"""
        owner_id = id(owner)
        if owner_id in cls._active_handlers:
            # 记录释放的内存
            if (owner_id in cls._memory_stats and
                handler_name in cls._memory_stats[owner_id]):
                freed_memory = cls._memory_stats[owner_id][handler_name]["estimated_memory"]
                cls._optimization_stats["memory_saved"] += freed_memory
                del cls._memory_stats[owner_id][handler_name]

            cls._active_handlers[owner_id].pop(handler_name, None)
            cls._access_times[owner_id].pop(handler_name, None)

            # 如果没有Handler了，清理整个条目
            if not cls._active_handlers[owner_id]:
                del cls._active_handlers[owner_id]
                if owner_id in cls._access_times:
                    del cls._access_times[owner_id]
                if owner_id in cls._memory_stats:
                    del cls._memory_stats[owner_id]

    @classmethod
    def cleanup_inactive_handlers(cls, max_idle_time: int = None):
        """清理不活跃的Handler"""
        if max_idle_time is None:
            max_idle_time = cls._cleanup_thresholds["idle_time"]

        current_time = time.time()
        cleanup_count = 0
        memory_freed = 0

        for owner_id, handlers in list(cls._active_handlers.items()):
            for handler_name, handler in list(handlers.items()):
                last_access = cls._access_times[owner_id].get(handler_name, current_time)

                # 检查是否超过清理阈值
                if (current_time - last_access) > max_idle_time:
                    # 检查是否有依赖
                    if not handler.dependents:
                        # 记录释放的内存
                        if (owner_id in cls._memory_stats and
                            handler_name in cls._memory_stats[owner_id]):
                            memory_freed += cls._memory_stats[owner_id][handler_name]["estimated_memory"]
                            del cls._memory_stats[owner_id][handler_name]

                        # 清理Handler
                        try:
                            handler.cleanup()
                        except Exception as e:
                            cls._log_error(f"Handler清理失败: {e}")

                        del handlers[handler_name]
                        if owner_id in cls._access_times:
                            cls._access_times[owner_id].pop(handler_name, None)

                        cleanup_count += 1

            # 清理空的owner条目
            if not handlers:
                del cls._active_handlers[owner_id]
                if owner_id in cls._access_times:
                    del cls._access_times[owner_id]
                if owner_id in cls._memory_stats:
                    del cls._memory_stats[owner_id]

        # 更新统计
        cls._optimization_stats["total_cleaned"] += cleanup_count
        cls._optimization_stats["memory_saved"] += memory_freed
        cls._optimization_stats["last_cleanup"] = current_time

        if cleanup_count > 0:
            cls._log_info(f"清理了 {cleanup_count} 个不活跃Handler，释放内存 {memory_freed} 字节")

        return cleanup_count

    @classmethod
    def get_total_memory_usage(cls) -> int:
        """获取总内存使用量"""
        total = 0
        for obj_stats in cls._memory_stats.values():
            for handler_stats in obj_stats.values():
                total += handler_stats["estimated_memory"]
        return total

    @classmethod
    def get_total_handler_count(cls) -> int:
        """获取总Handler数量"""
        total = 0
        for obj_handlers in cls._active_handlers.values():
            total += len(obj_handlers)
        return total

    @classmethod
    def get_optimization_report(cls) -> dict:
        """获取内存优化报告"""
        total_memory = cls.get_total_memory_usage()
        total_handlers = cls.get_total_handler_count()

        # 计算优化率
        if cls._optimization_stats["total_created"] > 0:
            cleanup_rate = (cls._optimization_stats["total_cleaned"] /
                          cls._optimization_stats["total_created"]) * 100
        else:
            cleanup_rate = 0

        # 计算缓存命中率
        total_accesses = (cls._optimization_stats["cache_hits"] +
                         cls._optimization_stats["cache_misses"])
        if total_accesses > 0:
            cache_hit_rate = (cls._optimization_stats["cache_hits"] / total_accesses) * 100
        else:
            cache_hit_rate = 0

        return {
            "current_memory_usage": total_memory,
            "current_handler_count": total_handlers,
            "total_created": cls._optimization_stats["total_created"],
            "total_cleaned": cls._optimization_stats["total_cleaned"],
            "memory_saved": cls._optimization_stats["memory_saved"],
            "cleanup_rate": f"{cleanup_rate:.1f}%",
            "cache_hit_rate": f"{cache_hit_rate:.1f}%",
            "memory_efficiency": f"{(cls._optimization_stats['memory_saved'] / max(total_memory, 1)) * 100:.1f}%"
        }

    @classmethod
    def _log_info(cls, message: str):
        """记录信息日志"""
        log_msg = f"[HandlerMemoryManager] {message}"
        if logger:
            logger.log_info(log_msg)
        else:
            print(log_msg)

    @classmethod
    def _log_error(cls, message: str):
        """记录错误日志"""
        log_msg = f"[HandlerMemoryManager ERROR] {message}"
        if logger:
            logger.log_err(log_msg)
        else:
            print(log_msg)
    
    @classmethod
    def get_memory_stats(cls) -> dict:
        """获取内存使用统计"""
        total_handlers = sum(len(handlers) for handlers in cls._active_handlers.values())
        total_owners = len(cls._active_handlers)
        
        return {
            "total_handlers": total_handlers,
            "total_owners": total_owners,
            "average_handlers_per_owner": total_handlers / max(total_owners, 1),
            "active_owners": list(cls._active_handlers.keys())
        }


class HandlerMixin:
    """
    为Evennia对象提供Handler功能的Mixin
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._handler_states = {}
    
    def get_handler(self, handler_name: str) -> Optional[BaseHandler]:
        """获取指定的Handler"""
        # 尝试从lazy_property获取
        if hasattr(self, handler_name):
            handler = getattr(self, handler_name)
            if isinstance(handler, BaseHandler):
                handler.access()
                return handler
        
        # 尝试动态创建
        handler = HandlerRegistry.create_handler(handler_name, self)
        if handler:
            setattr(self, f"_lazy_{handler_name}", handler)
            return handler
        
        return None
    
    def activate_handler(self, handler_name: str, **initialization_data):
        """激活指定的Handler"""
        handler = self.get_handler(handler_name)
        if handler:
            handler.initialization_data.update(initialization_data)
            handler.initialize()
            return True
        return False
    
    def deactivate_handler(self, handler_name: str):
        """停用指定的Handler"""
        handler = self.get_handler(handler_name)
        if handler:
            handler.cleanup()
            # 从lazy_property缓存中移除
            cache_key = f"_lazy_{handler_name}"
            if hasattr(self, cache_key):
                delattr(self, cache_key)
            return True
        return False
    
    def list_active_handlers(self) -> List[str]:
        """列出活跃的Handler"""
        active_handlers = []
        
        # 检查所有可能的lazy_property
        for attr_name in dir(self):
            if attr_name.startswith("_lazy_"):
                handler_name = attr_name[6:]  # 移除 "_lazy_" 前缀
                handler = getattr(self, attr_name, None)
                if handler and isinstance(handler, BaseHandler) and handler.is_active:
                    active_handlers.append(handler_name)
        
        return active_handlers
    
    def save_handler_states(self):
        """保存Handler状态"""
        states = {}
        for handler_name in self.list_active_handlers():
            handler = self.get_handler(handler_name)
            if handler:
                states[handler_name] = handler.serialize_state()
        
        if hasattr(self, 'attributes'):
            self.attributes.add("handler_states", states, category="system")
    
    def restore_handler_states(self):
        """恢复Handler状态"""
        if hasattr(self, 'attributes'):
            states = self.attributes.get("handler_states", {}, category="system")
            
            for handler_name, state_data in states.items():
                if self.activate_handler(handler_name):
                    handler = self.get_handler(handler_name)
                    if handler:
                        handler.deserialize_state(state_data)


# 装饰器用于Handler方法
def handler_method(func):
    """标记Handler方法的装饰器"""
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        if not self.is_active:
            self.log_info("Handler is not active, activating...")
            self.initialize()

        self.access()
        return func(self, *args, **kwargs)

    return wrapper


class HandlerCommunicationBus:
    """
    Handler间通信总线 - 实现Handler间的事件通信机制
    """

    _instance = None
    _subscribers: Dict[str, List[Callable]] = defaultdict(list)
    _event_history: List[HandlerEvent] = []
    _max_history = 1000

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    @classmethod
    def subscribe(cls, event_type: HandlerEventType, handler_name: str, callback: Callable):
        """订阅Handler事件"""
        bus = cls.get_instance()
        subscription_key = f"{event_type.value}:{handler_name}"
        bus._subscribers[subscription_key].append(callback)

    @classmethod
    def subscribe_all(cls, handler_name: str, callback: Callable):
        """订阅所有事件类型"""
        for event_type in HandlerEventType:
            cls.subscribe(event_type, handler_name, callback)

    @classmethod
    def unsubscribe(cls, event_type: HandlerEventType, handler_name: str, callback: Callable):
        """取消订阅Handler事件"""
        bus = cls.get_instance()
        subscription_key = f"{event_type.value}:{handler_name}"
        if subscription_key in bus._subscribers:
            try:
                bus._subscribers[subscription_key].remove(callback)
            except ValueError:
                pass

    @classmethod
    def publish_event(cls, event: HandlerEvent):
        """发布Handler事件"""
        bus = cls.get_instance()

        # 添加到历史记录
        bus._event_history.append(event)
        if len(bus._event_history) > bus._max_history:
            bus._event_history.pop(0)

        # 通知订阅者
        subscription_keys = [
            f"{event.event_type.value}:{event.source_handler}",
            f"{event.event_type.value}:*",  # 通配符订阅
        ]

        if event.target_handler:
            subscription_keys.append(f"{event.event_type.value}:{event.target_handler}")

        for key in subscription_keys:
            for callback in bus._subscribers.get(key, []):
                try:
                    callback(event)
                except Exception as e:
                    print(f"Handler事件回调失败: {e}")

    @classmethod
    def get_event_history(cls, handler_name: str = None,
                         event_type: HandlerEventType = None) -> List[HandlerEvent]:
        """获取事件历史"""
        bus = cls.get_instance()
        events = bus._event_history

        if handler_name:
            events = [e for e in events if
                     e.source_handler == handler_name or e.target_handler == handler_name]

        if event_type:
            events = [e for e in events if e.event_type == event_type]

        return events


class HandlerDependencyManager:
    """
    Handler依赖管理器 - 管理Handler间的依赖关系
    """

    _dependencies: Dict[str, List[str]] = defaultdict(list)  # {handler: [dependencies]}
    _dependents: Dict[str, List[str]] = defaultdict(list)    # {handler: [dependents]}

    @classmethod
    def add_dependency(cls, handler_name: str, dependency_name: str):
        """添加依赖关系"""
        if dependency_name not in cls._dependencies[handler_name]:
            cls._dependencies[handler_name].append(dependency_name)
            cls._dependents[dependency_name].append(handler_name)

            # 发布事件
            event = HandlerEvent(
                HandlerEventType.HANDLER_DEPENDENCY_ADDED,
                handler_name,
                dependency_name,
                {"dependency": dependency_name}
            )
            HandlerCommunicationBus.publish_event(event)

    @classmethod
    def remove_dependency(cls, handler_name: str, dependency_name: str):
        """移除依赖关系"""
        if dependency_name in cls._dependencies[handler_name]:
            cls._dependencies[handler_name].remove(dependency_name)
            cls._dependents[dependency_name].remove(handler_name)

            # 发布事件
            event = HandlerEvent(
                HandlerEventType.HANDLER_DEPENDENCY_REMOVED,
                handler_name,
                dependency_name,
                {"dependency": dependency_name}
            )
            HandlerCommunicationBus.publish_event(event)

    @classmethod
    def get_dependencies(cls, handler_name: str) -> List[str]:
        """获取Handler的依赖列表"""
        return cls._dependencies.get(handler_name, [])

    @classmethod
    def get_dependents(cls, handler_name: str) -> List[str]:
        """获取依赖此Handler的列表"""
        return cls._dependents.get(handler_name, [])

    @classmethod
    def check_circular_dependency(cls, handler_name: str, dependency_name: str) -> bool:
        """检查是否会产生循环依赖"""
        def has_path(start: str, end: str, visited: set = None) -> bool:
            if visited is None:
                visited = set()

            if start == end:
                return True

            if start in visited:
                return False

            visited.add(start)

            for dep in cls._dependencies.get(start, []):
                if has_path(dep, end, visited.copy()):
                    return True

            return False

        return has_path(dependency_name, handler_name)

    @classmethod
    def get_dependency_graph(cls) -> dict:
        """获取完整的依赖关系图"""
        return {
            "dependencies": dict(cls._dependencies),
            "dependents": dict(cls._dependents)
        }

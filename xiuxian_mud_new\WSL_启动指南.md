# WSL环境下Evennia修仙MUD服务器启动指南

## 概述
本文档提供在Windows Subsystem for Linux (WSL) 环境下启动和管理Evennia修仙MUD服务器的详细指导。

## 环境要求

### 系统要求
- Windows 10/11 with WSL2
- Ubuntu 20.04+ (推荐22.04)
- Python 3.8+ (推荐3.12)
- 至少2GB可用内存
- 至少1GB可用磁盘空间

### 预安装软件
```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装必要的开发工具
sudo apt install -y build-essential python3-dev python3-pip python3-venv
sudo apt install -y git curl wget nano vim
```

## 项目目录结构

```
NewEvennia/xiuxian_mud_new/
├── server/                 # 服务器配置和日志
│   ├── conf/              # 配置文件
│   └── logs/              # 日志文件
├── world/                 # 游戏世界内容
├── typeclasses/           # 游戏对象类定义
├── commands/              # 游戏命令
├── systems/               # 游戏系统
├── components/            # 组件系统
├── venv/                  # Python虚拟环境
└── manage.py             # Django管理脚本
```

## 启动前检查清单

### 1. 检查虚拟环境
```bash
# 进入项目目录
cd /mnt/c/Users/<USER>/Documents/GitHub/NewEvennia/xiuxian_mud_new

# 检查虚拟环境是否存在
ls -la venv/

# 如果不存在，创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install evennia
```

### 2. 检查数据库状态
```bash
# 检查数据库迁移状态
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia migrate --check

# 如果需要迁移
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia migrate
```

### 3. 检查配置文件
```bash
# 检查主配置文件
cat server/conf/settings.py | grep -E "SECRET_KEY|DATABASES|ALLOWED_HOSTS"

# 检查私密配置文件
ls -la server/conf/secret_settings.py
```

## 服务器启动步骤

### 标准启动流程

#### 1. 环境设置
```bash
# 设置PATH环境变量（重要！）
export PATH="$PWD/venv/bin:$PATH"

# 或者使用完整路径启动
alias evennia="PATH='$PWD/venv/bin:$PATH' ./venv/bin/evennia"
```

#### 2. 启动服务器
```bash
# 方法1: 使用PATH设置
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia start

# 方法2: 直接使用完整路径
./venv/bin/evennia start

# 方法3: 如果设置了alias
evennia start
```

#### 3. 验证启动状态
```bash
# 检查服务器状态
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia status

# 预期输出:
# Portal: RUNNING (pid XXXX)
# Server: RUNNING (pid YYYY)
```

### 常用端口说明

| 端口 | 服务 | 用途 | 访问方式 |
|------|------|------|----------|
| 4000 | Telnet | 传统MUD客户端连接 | telnet localhost 4000 |
| 4001 | Web代理 | Web界面和客户端 | http://localhost:4001 |
| 4002 | WebSocket | Web客户端实时通信 | 自动连接 |
| 4005 | Web内部 | 内部Web服务 | 仅内部使用 |
| 4006 | AMP内部 | 内部通信协议 | 仅内部使用 |

## 服务器管理命令

### 基本操作
```bash
# 启动服务器
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia start

# 停止服务器
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia stop

# 重启服务器
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia restart

# 重载服务器（不断开连接）
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia reload

# 查看状态
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia status

# 查看服务器信息
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia info
```

### 开发工具
```bash
# 进入Django shell
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia shell

# 运行测试
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia test

# 查看日志
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia --log

# 数据库shell
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia dbshell
```

### 日志管理
```bash
# 实时查看服务器日志
tail -f server/logs/server.log

# 实时查看Portal日志
tail -f server/logs/portal.log

# 查看HTTP请求日志
tail -f server/logs/http_requests.log

# 查看最近50行日志
tail -n 50 server/logs/server.log
```

## 客户端连接方式

### Web客户端（推荐新手）
1. 打开浏览器访问: http://localhost:4001
2. 点击"Connect"进入游戏界面
3. 使用以下命令:
   - 创建账户: `create <用户名> <密码>`
   - 连接账户: `connect <用户名> <密码>`
   - 创建角色: `@charcreate <角色名>`

### Telnet客户端
```bash
# 使用系统telnet
telnet localhost 4000

# 使用nc (netcat)
nc localhost 4000

# 推荐的MUD客户端
# - MUSHclient
# - Mudlet
# - TinTin++
```

## 故障排除

### 常见问题及解决方案

#### 1. 无法找到evennia命令
```bash
# 问题: -bash: evennia: command not found
# 解决: 设置正确的PATH
export PATH="$PWD/venv/bin:$PATH"

# 或使用完整路径
./venv/bin/evennia status
```

#### 2. 无法找到twistd
```bash
# 问题: No such file or directory: 'twistd'
# 解决: 确保PATH包含虚拟环境
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia start

# 检查twistd是否存在
ls -la venv/bin/twistd
```

#### 3. 端口被占用
```bash
# 检查端口占用
netstat -tulpn | grep -E "4000|4001|4002"

# 或使用ss命令
ss -tulpn | grep -E "4000|4001|4002"

# 杀死占用进程
sudo kill -9 <PID>
```

#### 4. 数据库错误
```bash
# 重新迁移数据库
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia migrate

# 如果迁移失败，备份并重置数据库
cp server/evennia.db3 server/evennia.db3.backup
rm server/evennia.db3
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia migrate
```

#### 5. 组件加载错误
```bash
# 检查组件导入
PATH="$PWD/venv/bin:$PATH" ./venv/bin/python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
import django
django.setup()
from components.cultivation_component import CultivationComponent
print('组件加载成功')
"
```

### 日志诊断
```bash
# 查看启动错误
grep -i error server/logs/server.log | tail -10

# 查看Django错误
grep -i "django\|traceback" server/logs/server.log | tail -20

# 查看组件错误
grep -i "component\|handler" server/logs/server.log | tail -15
```

## 开发模式启动

### 调试模式
```bash
# 设置调试环境变量
export DJANGO_DEBUG=True

# 使用调试模式启动
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia start --log
```

### 自动重载模式
```bash
# 代码修改后自动重载
PATH="$PWD/venv/bin:$PATH" ./venv/bin/evennia reload
```

## 性能优化

### 系统资源监控
```bash
# 监控内存使用
free -h

# 监控CPU使用
top -p $(pgrep -f evennia)

# 监控磁盘空间
df -h
```

### WSL特定优化
```bash
# 限制WSL内存使用（在Windows PowerShell中执行）
# 创建 %UserProfile%\.wslconfig 文件
# [wsl2]
# memory=4GB
# processors=2

# 重启WSL
wsl --shutdown
```

## 备份和维护

### 定期备份
```bash
# 备份数据库
cp server/evennia.db3 "server/backups/evennia_$(date +%Y%m%d_%H%M%S).db3"

# 备份配置文件
tar -czf "backups/config_$(date +%Y%m%d).tar.gz" server/conf/

# 备份世界文件
tar -czf "backups/world_$(date +%Y%m%d).tar.gz" world/
```

### 日志轮转
```bash
# 压缩旧日志
gzip server/logs/server.log.old

# 清理大日志文件
> server/logs/server.log
```

## 快速启动脚本

创建便捷启动脚本:

```bash
# 创建启动脚本
cat > start_mud.sh << 'EOF'
#!/bin/bash

# 修仙MUD启动脚本
cd /mnt/c/Users/<USER>/Documents/GitHub/NewEvennia/xiuxian_mud_new

echo "正在启动修仙MUD服务器..."

# 设置环境变量
export PATH="$PWD/venv/bin:$PATH"

# 检查服务器状态
if ./venv/bin/evennia status | grep -q "RUNNING"; then
    echo "服务器已在运行中"
    ./venv/bin/evennia status
else
    echo "启动服务器..."
    ./venv/bin/evennia start
    
    # 等待启动完成
    sleep 3
    
    # 显示状态
    ./venv/bin/evennia status
    echo ""
    echo "服务器已启动！"
    echo "Web客户端: http://localhost:4001"
    echo "Telnet连接: telnet localhost 4000"
    echo ""
    echo "管理命令:"
    echo "  停止服务器: ./stop_mud.sh"
    echo "  重载服务器: PATH=\"\$PWD/venv/bin:\$PATH\" ./venv/bin/evennia reload"
    echo "  查看日志: tail -f server/logs/server.log"
fi
EOF

# 创建停止脚本
cat > stop_mud.sh << 'EOF'
#!/bin/bash

cd /mnt/c/Users/<USER>/Documents/GitHub/NewEvennia/xiuxian_mud_new
export PATH="$PWD/venv/bin:$PATH"

echo "正在停止修仙MUD服务器..."
./venv/bin/evennia stop
echo "服务器已停止"
EOF

# 设置执行权限
chmod +x start_mud.sh stop_mud.sh

echo "启动脚本已创建："
echo "  启动: ./start_mud.sh"
echo "  停止: ./stop_mud.sh"
```

## 总结

使用本指南，你应该能够:
1. 成功启动Evennia修仙MUD服务器
2. 通过Web或Telnet客户端连接游戏
3. 诊断和解决常见问题
4. 进行日常维护和管理

如遇到本指南未覆盖的问题，请检查日志文件或参考Evennia官方文档。
"""
Cultivation Component for xiuxian_mud

This component encapsulates all logic related to character cultivation,
including spiritual energy gain, breakthroughs, and techniques.
It relies on the character's <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for data storage and
the central `world.rules` for game balance configurations.
"""
import time
import random
from evennia.contrib.base_systems.components import Component # type: ignore
from world.rules import REALM_DEFINITIONS, TECHNIQUE_LIBRARY

class CultivationComponent(Component):
    """
    Manages a character's cultivation progression.
    """
    # The name of the component, used for retrieval.
    name = "cultivation"

    def start_cultivation(self, technique_name: str = None) -> bool:
        """
        Begins a cultivation session for the character.
        
        Args:
            technique_name (str, optional): The specific technique to use. 
                                            If None, a suitable default is chosen.
        
        Returns:
            bool: True if cultivation started, False otherwise.
        """
        if self.owner.db.is_cultivating:
            self.owner.msg("你已经在修炼中了。")
            return False

        technique = self._get_technique(technique_name)
        if not technique:
            return False

        self.owner.db.is_cultivating = True
        self.owner.db.cultivation_technique = technique["name"]
        self.owner.db.cultivation_start_time = time.time()
        
        self.owner.msg(f"你开始运转《{technique['name']}》，进入了修炼状态。")
        return True

    def stop_cultivation(self) -> dict:
        """
        Stops the current cultivation session and calculates the gains.
        
        Returns:
            dict: A dictionary detailing the results of the session.
        """
        if not self.owner.db.is_cultivating:
            return {"success": False, "message": "你当前并未在修炼。"}

        cultivation_time = time.time() - self.owner.db.cultivation_start_time
        technique_name = self.owner.db.cultivation_technique
        technique = TECHNIQUE_LIBRARY.get(technique_name or "")

        # Base gain is 1 point per minute
        base_gain = cultivation_time / 60
        efficiency_multiplier = technique["efficiency"] if technique else 1.0
        
        # Realm bonus
        realm_tier = self.owner.traits.realm_tier.value
        realm_multiplier = realm_tier * 0.5 + 1.0
        
        # Random factor
        random_factor = random.uniform(0.8, 1.2)
        
        final_gain = int(base_gain * efficiency_multiplier * realm_multiplier * random_factor)
        
        self.owner.traits.cultivation.value += final_gain

        self.owner.db.is_cultivating = False
        self.owner.db.cultivation_technique = None
        self.owner.db.cultivation_start_time = 0

        result = {
            "success": True,
            "cultivation_time": cultivation_time,
            "technique_used": technique['name'] if technique else "无",
            "cultivation_points_gained": final_gain,
            "total_cultivation_points": self.owner.traits.cultivation.value
        }
        self.owner.msg(f"你结束了本次修炼，获得了 {final_gain} 点修为。")
        return result

    def get_cultivation_progress(self) -> dict:
        """
        Gets the character's current cultivation progress.
        
        Returns:
            dict: A dictionary containing detailed progress info.
        """
        realm = self.owner.traits.realm.value
        realm_level = self.owner.traits.realm_level.value
        realm_def = REALM_DEFINITIONS.get(realm)
        
        if not realm_def:
            return {}

        required_points = self._get_breakthrough_requirement(realm, realm_level)
        current_points = self.owner.traits.cultivation.value

        return {
            "realm": realm,
            "level": realm_level,
            "cultivation_points": current_points,
            "required_points": required_points,
            "progress_percentage": min((current_points / required_points) * 100, 100) if required_points > 0 else 100,
            "can_breakthrough": current_points >= required_points
        }

    def attempt_breakthrough(self) -> dict:
        """
        Attempts to break through to the next level or realm.

        Returns:
            dict: A dictionary with the result of the attempt.
        """
        progress = self.get_cultivation_progress()
        if not progress.get("can_breakthrough"):
            return {"success": False, "message": "你的修为尚未达到瓶颈。"}

        current_realm_name = self.owner.traits.realm.value
        current_level = self.owner.traits.realm_level.value
        realm_def = REALM_DEFINITIONS[current_realm_name]

        # Basic success chance
        success_chance = 0.8 

        if random.random() < success_chance:
            # Success!
            self.owner.traits.cultivation.value -= progress["required_points"]
            
            if current_level < realm_def["max_level"]:
                # Level up within the same realm
                self.owner.traits.realm_level.value += 1
                new_level = self.owner.traits.realm_level.value
                self.owner.msg(f"|g恭喜！你成功突破至 {current_realm_name} 第 {new_level} 层！|n")
                return {"success": True, "message": f"成功突破至{current_realm_name}第{new_level}层。"}
            else:
                # Advance to the next realm
                next_realm_tier = realm_def["tier"] + 1
                next_realm = next((r for r, d in REALM_DEFINITIONS.items() if d["tier"] == next_realm_tier), None)
                if next_realm:
                    self.owner.traits.realm.value = next_realm
                    self.owner.traits.realm_level.value = 1
                    self.owner.traits.realm_tier.value = next_realm_tier
                    self.owner.msg(f"|g天地变色！你成功渡劫，踏入了全新的境界：{next_realm}！|n")
                    return {"success": True, "message": f"成功突破至{next_realm}。"}
                else:
                    self.owner.msg("你已达到当前世界的顶峰！")
                    return {"success": False, "message": "已是最高境界。"}
        else:
            # Failure
            lost_points = int(progress["required_points"] * 0.3)
            self.owner.traits.cultivation.value -= lost_points
            self.owner.msg(f"|r突破失败！你损失了 {lost_points} 点修为，境界不稳。|n")
            return {"success": False, "message": "突破失败，损失了部分修为。"}

    def _get_technique(self, technique_name: str = None):
        """Helper to get a technique, checking requirements."""
        if technique_name:
            technique = TECHNIQUE_LIBRARY.get(technique_name)
            if not technique:
                self.owner.msg(f"未找到名为《{technique_name}》的功法。")
                return None
            if not self._check_technique_requirements(technique):
                self.owner.msg(f"你的境界尚不足以修炼《{technique_name}》。")
                return None
            return technique
        else:
            # Find the best suitable technique if none is provided
            available = [
                t for t in TECHNIQUE_LIBRARY.values() 
                if self._check_technique_requirements(t)
            ]
            if not available:
                self.owner.msg("你目前没有任何可以修炼的功法。")
                return None
            # Return the one with the highest efficiency
            return max(available, key=lambda t: t["efficiency"])
            
    def _check_technique_requirements(self, technique: dict) -> bool:
        """Checks if the character meets the requirements for a technique."""
        reqs = technique.get("requirements", {})
        current_tier = self.owner.traits.realm_tier.value
        return current_tier >= reqs.get("realm_tier", 1)
        
    def _get_breakthrough_requirement(self, realm: str, level: int) -> int:
        """Calculates the cultivation points needed for the next breakthrough."""
        realm_def = REALM_DEFINITIONS.get(realm)
        if not realm_def:
            return 99999999
        
        base_req = realm_def["breakthrough_req"]
        # Increase requirement for each level within the realm
        return int(base_req * (1 + level * 0.5))

    def get_info(self):
        """Returns a dict with all cultivation-related information."""
        progress = self.get_cultivation_progress()
        return {
            "Realm": f"{progress.get('realm', 'N/A')} "
                     f"Level {progress.get('level', 'N/A')}",
            "Cultivation": f"{progress.get('cultivation_points', 0)} / "
                           f"{progress.get('required_points', 'N/A')}",
            "Progress": f"{progress.get('progress_percentage', 0):.2f}%",
            "Can Breakthrough": progress.get('can_breakthrough', False),
            "Is Cultivating": self.owner.db.is_cultivating or False,
            "Current Technique": self.owner.db.cultivation_technique or "None",
        } 